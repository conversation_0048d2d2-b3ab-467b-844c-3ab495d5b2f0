<?php
namespace App\Repositories\ModelSystem;

use App\Repositories\EloquentRepository;

class ModelSystemRepository extends EloquentRepository implements ModelSystemRepositoryInterface
{
	public function getModel()
	{
		return \App\Models\ModelSystem::class;
	}
	
	public function getModelSystem()
	{
		// Danh sách các model tương ứng với bảng
		$models = [
			'users'       => \App\Models\User::class,
			'departments' => \App\Models\Department::class,
			// Thêm các model khác nếu cần
		];

		return $models;
	}
}
?>