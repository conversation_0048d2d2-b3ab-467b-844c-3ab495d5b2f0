<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-4">
				<FormKit
					ref="dynamicForm"
					type="form"
					:actions="false"
					incomplete-message=" "
					@submit="handleSubmitForm"
				>
					<Form ref="form" @submit="handleSubmitForm" :validation-schema="schema(sortedFormFields)">
						<CCardBody>
							<div class="mb-4">
								<CCol :xs="6">
									<FormKit
										type="checkbox"
										:label="$t('job.is_multiple')"
										name="is_multiple"
										v-model="state.formDataJob.is_multiple"
									/>
								</CCol>
								<CCol :xs="6">
									<FormKit
										type="text"
										:label="$t('job.name')"
										:floating-label="true"
										name="name"
										v-model="state.formDataJob.name"
										validation="required|length:1,200"
										:validation-messages="{
											required: `${$t('job.name')} ${$t('form.validate.required')}`,
											length: `${$t('job.name')} ${$t('form.validate.name_length')}`
										}" 
									/>
								</CCol>
								<CCol :xs="6" class="mb-3">
									<label class="mb-2">
										{{ $t('job.workflow') }}
										<span class="text-danger">*</span>
									</label>
									<Field 
										name="workflow"
										v-slot="{ field }"
									>
										<Multiselect
											v-bind="field"
											v-model="state.formDataJob.workflow"
											:placeholder="$t('job.workflow')"
											:close-on-select="false"
											:filter-results="false"
											:resolve-on-load="false"
											:infinite="true"
											:limit="10"
											:clear-on-search="true"
											:searchable="true"
											:delay="0"
											:min-chars="0"
											:object="true"
											:options="async (query) => {
												return await debouncedGetOptionWorkflows(query)
											}"
											@change="changeOptionWorkflow($event)"
											@open="debouncedGetOptionWorkflows('')"
											:can-clear="false"
											class="select-workflow"
										/>
									</Field>
									<ErrorMessage
										as="div"
										name="workflow"
										class="text-danger"
									/>
								</CCol>
								<CCol :xs="6" class="mb-3">
									<div class="d-flex align-items-center">
										<label class="mb-2">
											{{ $t('job.job_manager') }}
											<span class="text-danger">*</span>
										</label>
										<span 
											class="material-symbols-outlined ms-1 cursor-pointer icon-info"
											v-b-tooltip.hover
											:title="$t('workflow.job_manager_desc')"
										>
											info
										</span>
									</div>
									<Field 
										name="job_manager"
											v-slot="{ field }"
										>
										<Multiselect
											mode="tags"
											v-model="state.formDataJob.job_manager"
											v-bind="field"
											:placeholder="$t('job.job_manager')"
											:object="true"
											:disabled="true"
										/>
									</Field>
									<ErrorMessage
										as="div"
										name="job_manager"
										class="text-danger"
									/>
								</CCol>
								<CCol :xs="6" class="mb-3">
									<label class="mb-1">
										{{ $t('job.followers') }}
									</label>
									<Multiselect
										mode="tags"
										v-model="state.formDataJob.followers"
										:placeholder="$t('job.followers')"
										:close-on-select="false"
										:filter-results="false"
										:resolve-on-load="false"
										:infinite="true"
										:limit="20"
										:clear-on-search="true"
										:searchable="true"
										:delay="0"
										:min-chars="0"
										:object="true"
										:options="async (query) => {
											return await debouncedGetOptionScopes(query)
										}"
										@open="debouncedGetOptionScopes('')"
										:can-clear="false"
									>
										<template v-slot:option="{ option }">
											<div class="custom-option">
												<div class="option-label mb-1">
													{{ option.label }}
												</div>
												<div class="option-description text-secondary">
													<small>
														<i>{{ option.description }}</i>
													</small>
												</div>
											</div>
										</template>
									</Multiselect>
								</CCol>
							</div>
							<!-- Dynamic Form Fields Component -->
							<DynamicFormFields
								:form-fields="sortedFormFields"
								:form-data="state.formData"
								:item-childrens="state.itemChildrens"
								:select-option-departments="state.selectOptionDepartments"
								:sub-column-table-description="state.subColumnTableDescription"
								:sub-column-table-option-selected="state.subColumnTableOptionSelected"
								:sub-column-table-description-children="state.subColumnTableDescriptionChildren"
								:sub-column-table-option-selected-children="state.subColumnTableOptionSelectedChildren"
								:formatted-formula-results="formattedFormulaResults"
								:max-files="state.maxFiles"
								:max-file-size="state.maxFileSize"
								:accepted-file-types="state.acceptedFileTypes"
								:get-option-users="debouncedGetOptionUsers"
								:get-option-column-data="debouncedGetOptionColumnData"
								@update-form-data="(key, value) => state.formData[key] = value"
								@update-files="updateFiles"
								@update-file-childrens="updateFileChildrens"
								@add-item="addItem"
								@remove-item="removeItem"
								@show-sub-column-table="showSubColumnTable"
								@show-sub-column-table-children="showSubColumnTableChildren"
							/>
							<div class="mb-3">
								<CCol :xs="6">
									<FormKit
										type="textarea"
										:label="$t('job.description')"
										:floating-label="true"
										name="description"
										v-model="state.formDataJob.description"
									/>
								</CCol>
								<CCol :xs="6">
									<label class="mb-1">
										{{ $t('job.files') }}
									</label>
									<FilePond
										:files="state.formDataJob.files"
										@updatefiles="(fileItemUploads) => updateFileDefaults(fileItemUploads)"
										@addfile="onAddFiles"
										className="file-pond"
										:labelIdle="$t('validate_field.file_upload.label_idle')"
										:allowMultiple="true"
										:maxFiles="state.maxFiles"
										:maxFileSize="state.maxFileSize"
										:acceptedFileTypes="state.acceptedFileTypes"
										:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
										:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
										:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
										:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
										:instantUpload="false"
										name="files"
										ref="files"
										credits="false"
										allow-reorder="true"
										item-insert-location="after"
										image-preview-min-height="60"
										image-preview-max-height="60"
									/>
								</CCol>
							</div>
						</CCardBody>
						<CCardFooter>
							<div class="d-flex justify-content-start">
								<CButton 
									type="submit"
									class="btn btn-primary m-1"
									@click="submitForm"
								>
									<span class="text-uppercase">
										{{ $t('job.save_update') }}
									</span>
								</CButton>
								<CButton 
									type="button"
									class="btn btn-light border m-1"
									@click="closeForm"
								>
									<span class="text-uppercase">
										{{ $t('job.close') }}
									</span>
								</CButton>
							</div>
						</CCardFooter>
					</Form>
				</FormKit>
			</CCard>
		</CCol>
	</CRow>
	<loading
		:isLoading="setIsLoading"
	/>
</template>

<script lang="ts">
import { defineComponent, reactive , ref, onMounted } from 'vue'
import { useI18n } from "vue-i18n";
import { useRouter } from 'vue-router';
import Multiselect from '@vueform/multiselect';
import Loading from '@/views/loading/Loading.vue'
import DynamicFormFields from '@/components/DynamicFormFields.vue'
import vueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import useForms from '@/composables/form';
import useJobs from '@/composables/job';
import useOptions from '@/composables/option';
import useDynamicFormLogic from '@/composables/useDynamicFormLogic';
import useDynamicFormState from '@/composables/useDynamicFormState';
import useOptionsHandlers from '@/composables/useOptionsHandlers';
import useFormValidation from '@/composables/useFormValidation';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { useToast } from 'vue-toast-notification';
import { WORKFLOWS } from "@/constants/constants";

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize);
  
export default defineComponent({
    name: "Job",

	components: {
        Multiselect,
		FilePond,
		Form,
		Field,
		ErrorMessage,
		Loading,
		DynamicFormFields
    },
  
    setup() {
		const { t }  = useI18n();
		const $toast = useToast();
		const dynamicForm: any = ref(null);
		const router = useRouter();

		// Use shared composables
		const {
			createDynamicFormState,
			useSortedFormFields,
			useFormulaCalculations
		} = useDynamicFormState();

		const {
			getOptionWorkflows: getOptionWorkflowsHandler,
			getOptionUsers: getOptionUsersHandler,
			getOptionDepartments: getOptionDepartmentsHandler,
			getOptionProcessScopes: getOptionProcessScopesHandler,
			getOptionColumnData: getOptionColumnDataHandler,
			getOptionProcessScopesnotDebounce: getOptionProcessScopesnotDebounceHandler
		} = useOptionsHandlers();

		const { 
			createDynamicSchema,
			validateRequiredFields
		} = useFormValidation(t);
		
		const {
			converFormFields,
			formattedFormulaChildrenResults,
			initializeValues: initializeValuesFromComposable,
			addItem: addItemFromComposable,
			removeItem: removeItemFromComposable,
			showSubColumnTable: showSubColumnTableFromComposable,
			showSubColumnTableChildren: showSubColumnTableChildrenFromComposable,
			updateFiles: updateFilesFromComposable,
			updateFileChildrens: updateFileChildrensFromComposable
		} = useDynamicFormLogic();



		// Use shared validation schema
		const schema = (field: any[]) => createDynamicSchema(field);

		// Use shared dynamic form state
		const dynamicFormState = createDynamicFormState();

		// Job-specific state (keep separate)
		const jobState = reactive({
			flowTransitions: [],
			formDataJob: {
				is_multiple: false,
				name: '',
				workflow: {},
				description: '',
				job_manager: [],
				followers: [],
				files: [],
			} as any,
			selectOptionSystemDefaults: [
				{ label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
			] as Array<any>,
		});

		// Combine states for backward compatibility
		const state = reactive({
			...dynamicFormState,
			...jobState
		});

		// Use shared computed properties
		const sortedFormFields = useSortedFormFields(state);
		const formulaCalculations = useFormulaCalculations(sortedFormFields, state.formData, state.itemChildrens);
		const formattedFormulaResults = formulaCalculations.formattedFormulaResults;

		const { getUserByOptionScopes } = useOptions();

		// Use shared option handlers
		const debouncedGetOptionWorkflows = getOptionWorkflowsHandler;
		const debouncedGetOptionUsers = getOptionUsersHandler;
		const getOptionDepartments = () => getOptionDepartmentsHandler(state.selectOptionDepartments);
		const debouncedGetOptionScopes = (query: string) => getOptionProcessScopesHandler(query, state.selectOptionSystemDefaults);
		const getOptionProcessScopesnotDebounce = (query: string) => getOptionProcessScopesnotDebounceHandler(query, state.selectOptionSystemDefaults);

		onMounted( async () => {
			await getOptionDepartments();
			await initializeValues();
		});

		const changeOptionWorkflow = async (workflow: any) => {
			if (!!workflow) {
				state.formDataJob = {
					is_multiple: false,
					name: state.formDataJob.name || '',
					workflow: {},
					description: '',
					job_manager: [],
					followers: [],
					files: [],
				} as any;
				state.formData = {} as any;
				state.itemChildrens = {} as { [key: string]: any[] };
				state.subColumnTableDescription = {} as { [key: string]: any };
				state.subColumnTableOptionSelected = {} as { [key: string]: any };
				state.subColumnTableDescriptionChildren = {} as { [key: string]: { [index: number]: any } };
				state.subColumnTableOptionSelectedChildren = {} as { [key: string]: { [index: number]: any } };
				const formId = workflow.formId;
				const stageId = WORKFLOWS.STAGE.START;
				state.dataFormFields = await getFieldsByFormId(formId, stageId);
				// state.flowTransitions = await getFlowTransitions(workflow.value);
				// Hiển thị followers
				if (!!workflow.followers) {
					const followers = workflow.followers;
					const dataOptionProcessScope = await getOptionProcessScopesnotDebounce('');
					if (dataOptionProcessScope) {
						const matchedFollowers = dataOptionProcessScope.filter((item: any) =>
							followers.includes(item.value)
						);
						state.formDataJob.followers = matchedFollowers;
					}
				}
				// Hiển thị jobManager
				if (!!workflow.jobManager) {
					const jobManager = workflow.jobManager;
					const optionScopes = [...jobManager];
					const datajobManager = await getUserByOptionScopes(optionScopes, workflow.value);
					state.formDataJob.job_manager = datajobManager;
				}
			}
		}

		const initializeValues = async () => {
			initializeValuesFromComposable(sortedFormFields.value, state.formData);
		};

		const addItem = (field: any) => {
			addItemFromComposable(field, state.itemChildrens);
		};

		const removeItem = (field: any, itemIndex: number) => {
			removeItemFromComposable(field, itemIndex, state.itemChildrens);
		};

		const { getFieldsByFormId } = useForms();
		const { setIsLoading, storeJob } = useJobs();

		const showSubColumnTable = (optionSelected: any, keyName: string) => {
			showSubColumnTableFromComposable(optionSelected, keyName, state.subColumnTableDescription, state.subColumnTableOptionSelected);
		}

		const showSubColumnTableChildren = (optionSelected: any, itemIndex: number, keyName: string) => {
			showSubColumnTableChildrenFromComposable(
				optionSelected,
				itemIndex,
				keyName,
				state.subColumnTableDescriptionChildren,
				state.subColumnTableOptionSelectedChildren
			);
		}

		// Use shared option handler
		const debouncedGetOptionColumnData = getOptionColumnDataHandler;

		const updateFiles = (fileItemUploads: any, fieldName: string) => {
			updateFilesFromComposable(fileItemUploads, fieldName, state.formData);
		};
		const updateFileChildrens = (fileItemUploads: any, itemChildren: object, fieldChildrenName: string) => {
			updateFileChildrensFromComposable(fileItemUploads, itemChildren, fieldChildrenName);
		};

		const updateFileDefaults = (fileItemUploads: any) => {
			state.formDataJob.files = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

		const onAddFiles = (error: any, file: any) => {
			if (error) {
				state.formDataJob.files = state.formDataJob.files.filter((item: any) => item.name !== file.filename);
				$toast.open({
					message: `${error.main} - ${error.sub}`,
					type: "warning",
					duration: 10000,
					dismissible: true,
					position: "bottom-right",
				});
			}
		};

		const closeForm = () => {
			router.push('/job');
		}

		const submitForm = () => {
			const node = dynamicForm.value.node;
			node.submit();
			handleSubmitForm();
		}

		const handleSubmitForm = async () => {
			if (setIsLoading.value) {
				return;
			}
			const errorMessages = [] as any;
			const nameJob = state.formDataJob.name;
			const workflowJob = state.formDataJob.workflow;
			const managerJob = state.formDataJob.job_manager;
			
			if (typeof nameJob === 'string' && nameJob.trim() === '') {
				errorMessages.push(`${t('job.name')} ${t('form.validate.required')}`);
			}

			if (typeof workflowJob === 'object' && Object.keys(workflowJob).length === 0) {
                errorMessages.push(`${t('job.workflow')} ${t('form.validate.required')}`);
            }

			if (typeof workflowJob === 'object' && Object.keys(workflowJob).length !== 0 && managerJob.length === 0) {
                errorMessages.push(`${t('job.job_manager')} ${t('form.validate.required')}`);
            }
			
			const formID = state.formDataJob.workflow.formId;

			// Use shared validation logic
			const validationErrors = validateRequiredFields(sortedFormFields.value, state.formData, state.itemChildrens);
			errorMessages.push(...validationErrors);
			
			if (errorMessages.length > 0) {
				// Kết hợp các thông báo lỗi thành một chuỗi, mỗi lỗi trên một dòng
				const combinedMessage = errorMessages.join('<br>');
				$toast.open({
					message: combinedMessage,
					type: "warning",
					duration: 10000,
					dismissible: true,
					position: "bottom-right",
				});
				
				return;
			}

			const node = dynamicForm.value.node
			const isValidFormKit = node && node.context?.state?.valid;

			if (isValidFormKit) {
				let config: object = {
					header : {
						'Content-Type' : 'multipart/form-data'
					}
				}
				const keyFileUploads = sortedFormFields.value.filter((field: any) => field.type === 'FILEUPLOAD').map((field: any) => field.name);
				const keyFileUploadChildrens = sortedFormFields.value.filter((field: any) => field.type === 'TABLE').flatMap((field: any) => field.childrens.filter((field_child: any) => field_child.type === 'FILEUPLOAD').map((field_child: any) => field_child.keyword));
				let formJobData = new FormData();
				keyFileUploads.forEach((key: string) => {
					if (state.formData.hasOwnProperty(key)) {
						state.formData[key].forEach((file: any, index: number) => {
							formJobData.append(`key_file_uploads[${key}][${index}]`, file);
						});
					}
				});
				Object.keys(state.itemChildrens).forEach((keyItemChildrens: string) => {
					keyFileUploadChildrens.forEach((keyFileUploadChildren: string) => {
						const checkKeyFileUploadChildren = state.itemChildrens[keyItemChildrens].some((item: any) => item.hasOwnProperty(keyFileUploadChildren));
						if (checkKeyFileUploadChildren) {
							state.itemChildrens[keyItemChildrens].forEach((item: any, itemIndex: number) => {
								if (item[keyFileUploadChildren]) {
									item[keyFileUploadChildren].forEach((file: any, fileIndex: number) => {
										formJobData.append(`key_file_upload_childrens[${itemIndex}][${keyFileUploadChildren}][${fileIndex}]`, file);
									});
								}
							});
						}
					});
				});
				
				Object.keys(state.itemChildrens).forEach((keyItemChildrens: string) => {
					formJobData.append(`key_tables[${keyItemChildrens}]`, JSON.stringify(state.itemChildrens[keyItemChildrens]));
				});

				if (state.formDataJob.files) {
					state.formDataJob.files.forEach((file: any, index: number) => {
						formJobData.append(`fileUploads[${index}]`, file);
					});
				}

				formJobData.append('formData', JSON.stringify(state.formData));
				formJobData.append('formDataJob', JSON.stringify(state.formDataJob));
				formJobData.append('formId', formID);
				formJobData.append('stageId', WORKFLOWS.STAGE.START);
				formJobData.append('actionId', WORKFLOWS.ACTION.CREATE);
				
				let response: any = await storeJob(formJobData, config);

				if (response && response.status === 'success') {
					$toast.open({
						message: t('toast.status.ACTION_SUCCESS'),
						type: "success",
						duration: 5000,
						dismissible: true,
						position: "bottom-right",
					});

					await router.push({ name: 'JobDetail', params: { id: response.job_id } });
				}
			} else {
				console.warn('Form chưa valid hoặc node không tồn tại, không thể submit!')
			}
		}

		return {
			state,
			schema,
			setIsLoading,
			debouncedGetOptionUsers,
			debouncedGetOptionWorkflows,
			debouncedGetOptionScopes,
			changeOptionWorkflow,
			dynamicForm,
			sortedFormFields,
			formattedFormulaResults,
			formattedFormulaChildrenResults: (nameKey: string) => formattedFormulaChildrenResults(nameKey, state.itemChildrens, state.formData),
			converFormFields,
			addItem,
      		removeItem,
			showSubColumnTable,
			showSubColumnTableChildren,
			debouncedGetOptionColumnData,
			updateFiles,
			updateFileChildrens,
			updateFileDefaults,
			onAddFiles,
			closeForm,
			submitForm,
			handleSubmitForm,
		}
    }
});
</script>

<style scoped>
	.cursor-pointer {
		cursor: pointer;
	}
	.column-width-td {
		min-width: 200px !important;
	}
	.card-footer {
		z-index: 10;
		position: sticky;
		left: 0px;
		bottom: 0px;
		width: 100%;
		background-color:#f8f9fa;
		padding: 10px;
	}
	.select-workflow {
		padding: 1.875px !important;
	}
	.disabled-column-table-description {
		pointer-events: none;
	}
	.icon-info {
		font-size: 16px !important;
		color: #0d6efd !important;
	}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>