<template>
	<div class="wrapper min-vh-100 d-flex flex-row align-items-center">
		<CContainer>
			<CRow class="justify-content-center">
				<CCol :md="4" class="w-form">
					<h4 class="text-center mb-4 title-logo text-uppercase">
						{{ $t('auth.title_logo') }}
					</h4>
					<CCardGroup>
						<CCard class="p-4">
							<CCardBody>
								<CForm @submit.prevent="saveUpdateNewPassword">
									<h5 class="text-center text-title text-uppercase">
										{{ $t('auth.new_pass') }}
									</h5>
									<br>
									<CInputGroup class="mb-3">
										<CInputGroupText>
											<span class="material-symbols-outlined">lock</span>
										</CInputGroupText>
										<CFormInput
											:type="state.showNewPassword ? 'text' : 'password'"
											:placeholder="$t('auth.new_password')"
											v-model="state.new_password"
                                            autocomplete="new_password"
                                            required
										/>
										<span class="password-toggle-icon material-symbols-outlined" @click="state.showNewPassword = !state.showNewPassword">
											{{ state.showNewPassword ? 'visibility' : 'visibility_off' }}
										</span>
									</CInputGroup>
									<CInputGroup class="mb-4">
										<CInputGroupText>
											<span class="material-symbols-outlined">lock</span>
										</CInputGroupText>
										<CFormInput
											:type="state.showNewPasswordConfirm ? 'text' : 'password'"
											:placeholder="$t('auth.new_password_confirm')"
											v-model="state.new_password_confirm"
                                            autocomplete="new_password_confirm"
                                            required
										/>
										<span class="password-toggle-icon material-symbols-outlined" @click="state.showNewPasswordConfirm = !state.showNewPasswordConfirm">
											{{ state.showNewPasswordConfirm ? 'visibility' : 'visibility_off' }}
										</span>
									</CInputGroup>
									<div class="mb-4">
                                        <small class="fst-italic text-info">
                                            <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="rgb(51 153 255)"><path d="M444-288h72v-240h-72v240Zm35.79-312q15.21 0 25.71-10.29t10.5-25.5q0-15.21-10.29-25.71t-25.5-10.5q-15.21 0-25.71 10.29t-10.5 25.5q0 15.21 10.29 25.71t25.5 10.5Zm.49 504Q401-96 331-126t-122.5-82.5Q156-261 126-330.96t-30-149.5Q96-560 126-629.5q30-69.5 82.5-122T330.96-834q69.96-30 149.5-30t149.04 30q69.5 30 122 82.5T834-629.28q30 69.73 30 149Q864-401 834-331t-82.5 122.5Q699-156 629.28-126q-69.73 30-149 30Z"/></svg>
											Mật khẩu ít nhất 8 ký tự bao gồm sự kết hợp của chữ hoa, chữ thường, số và ký tự đặc biệt.
                                        </small>
                                    </div>
									<div class="d-grid mb-4">
                                        <CButton 
                                            type="submit"
                                            color="primary" 
                                            class="px-4"
                                        >
                                        	<span>{{ $t('auth.confirm_reset') }}</span>
                                        </CButton>
                                    </div>
								</CForm>
								<div class="d-flex justify-content-center">
									<router-link 
										:to="{ name: 'login'}" 
										class="text-link" 
									>
										{{ $t('auth.back_login') }}
									</router-link>
								</div>
							</CCardBody>
						</CCard>
					</CCardGroup>
				</CCol>
			</CRow>
		</CContainer>
	</div>

	<loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive } from 'vue'
import useAuth from '@/composables/auths'
import { useRoute } from 'vue-router';
import Loading from '@/views/loading/Loading.vue'


export default defineComponent({
    name: "NewPassword",

	components: {
        Loading,
    },

    setup() {
        const route: any = useRoute()
        const state = reactive({
			showNewPassword: false,
            showNewPasswordConfirm: false,
            new_password: '',
            new_password_confirm: '',
        });

        const { setIsLoading, updateNewPassword } = useAuth();

        const saveUpdateNewPassword = async (): Promise<void> => {		
            await updateNewPassword(route.params.token, state.new_password, state.new_password_confirm);
        }

        return {
            state,
			setIsLoading,
            saveUpdateNewPassword,
        }
    }
});
</script>

<style scoped>
	.w-form {
		min-width: 460px !important;
	}
	.title-logo {
		font-size: 24px;
    	color: #6f42c1;
		font-weight: 600;
	}
	.text-title {
		color: #6f42c1;
	}
	.text-link {
		color: #6f42c1;
		font-size: 15px;
		text-decoration: none;
	}
	.password-toggle-icon {
		position: absolute;
		top: 50%;
		right: 10px;
		transform: translateY(-50%);
		cursor: pointer;
		color: #757575;
		background: none;
		border: none;
		outline: none;
		user-select: none;
		z-index: 10;
	}
	.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
		border-radius: 3px !important;
	}
</style>