import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";
import { Meta, Link } from "@/types/index";

export default function useUsers() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);
    const paginate = reactive({
        meta: {
            from: '',
            to: '',
            total: '',
            lastPage: '',
            currentPage: '',
            perPage: '',
        } as Meta,
        links: {
            prev: '',
            next: '',
        } as Link,
    });

    const dataUsers = ref<any>([]);
    const dataCounts = ref<any>([]);
    const userDetail = ref<any>(null);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const getAllUsers = async (
        page: number,
        perPage: number,
        valueTabActived: string,
    ) => {
        setIsLoading.value = true;
        try {
            const tab = valueTabActived !== '' ? { tab: valueTabActived } : null;
            let response = await axios.get('/api/users', {
                params: {
                    tab: tab !== null ? valueTabActived : tab,
                    page: page,
                    perPage: perPage,
                }
            });
            
            if (response.data.status == 'success') {
                dataCounts.value          = response.data.counts;
                dataUsers.value           = response.data.users.data;
                paginate.links.prev       = response.data.users.links.prev;
                paginate.links.next       = response.data.users.links.next; 
                paginate.meta.currentPage = response.data.users.current_page;
                paginate.meta.perPage     = response.data.users.per_page;
                paginate.meta.from        = response.data.users.from;
                paginate.meta.to          = response.data.users.to;
                paginate.meta.total       = response.data.users.total;
                paginate.meta.lastPage    = response.data.users.last_page;
            }

            const query = {
                ...tab
            };

            await router.push({ name: 'UserListData', query: { ...query, page, perPage,  } }).catch(()=>{});

        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getAllUsers(page, perPage, valueTabActived);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    const updateUserRoles = async (userId: string, roleIds: string[]) => {
        setIsLoading.value = true;
        try {
            let response = await axios.put(`/api/users/${userId}/roles`, {
                role_ids: roleIds
            });

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    updateUserRoles(userId, roleIds);
                }, 1000);
                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    return {
        setIsLoading,
        paginate,
        dataUsers,
        dataCounts,
        userDetail,
        getAllUsers,
        updateUserRoles,
    }
}
