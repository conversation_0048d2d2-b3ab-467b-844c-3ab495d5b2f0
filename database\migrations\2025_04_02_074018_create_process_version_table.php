<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('process_version', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('process_id');  // Thuộc quy trình
            $table->uuid('form_id');  // Thuộc form
            $table->json('scope_use')->nullable();  // Phạm vi áp dụng
            $table->json('followers')->nullable();  // Người theo dõi
            $table->json('process_manager');  // Người quản lý quy trình
            $table->json('job_manager');  // Người quản lý công việc
            $table->integer('version_number'); // <PERSON><PERSON> phiên bản của quy trình
            $table->json('change_log');  // <PERSON><PERSON> tả những thay đổi
            $table->boolean('is_active')->default(true)->comment('0 - Không hoạt động, 1 - Hoạt động');  // Trạng thái hoạt động
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('process_version', function (Blueprint $table) {
            $table->index(['id', 'process_id', 'form_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('process_version');
    }
};
