<template>
    <div
        v-if="isVisible"
        :style="{ 
            position: 'absolute',
            top: `${position.y}px`, 
            left: `${position.x}px`,
            zIndex: 9999999
        }"
        class="dropdown-menu show position-absolute"
        >
        <ul class="list-unstyled mb-0">
            <li v-for="(option, index) in options" :key="index">
            <button class="dropdown-item" @click="handleOption(option)">
                <span class="material-symbols-outlined me-1 mb-1">{{ option.icon }}</span>
                {{ option.label }}
            </button>
            </li>
        </ul>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ContextMenu',
    props: {
        options: {
            type: Array,
            required: true
        },
        isVisible: {
            type: Boolean,
            required: true
        },
        position: {
            type: Object,
            required: true
        }
    },
    emits: ['selectOption'],
    setup(props, { emit }) {
        const handleOption = (option) => {
            emit('selectOption', option);
        };
        
        return {
            handleOption
        };
    }
  };
  </script>
  
  <style scoped>
    .dropdown-menu {
        background-color: white;
        border: 1px solid #ccc;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        z-index: 1000;
    }
    
    .dropdown-item {
        cursor: pointer;
    }
    
    .dropdown-item:hover {
        background-color: #f0f0f0;
    }
    
    .material-symbols-outlined {
        font-size: 18px;
        vertical-align: middle;
    }
  </style>
  