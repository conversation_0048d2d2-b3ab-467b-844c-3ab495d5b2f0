<?php

namespace App\Enums;

use Illuminate\Support\Str;

enum SaveJobStatus: string
{
    case ALL = 'all'; // Tất cả
    case PENDING = 'pending'; // Chờ xử lý
    case PROCESSING = 'processing'; // <PERSON><PERSON> xử lý
    case COMPLETED = 'completed'; // Ho<PERSON><PERSON> thành
    case CANCEL = 'cancel'; // Không xử lý

    public function label(): string
    {
        $file = 'enums';
        $enum_key = Str::snake(class_basename(static::class));
        $case_value = $this->value;
        $translation_key = "{$file}.{$enum_key}.{$case_value}";
        return __($translation_key);
    }
}