{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "eslint": "^9.13.0", "eslint-plugin-vue": "^9.28.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.47", "sass": "^1.80.3", "tailwindcss": "^3.4.13", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.8"}, "dependencies": {"@coreui/coreui": "^5.1.2", "@coreui/icons": "^3.0.1", "@coreui/icons-vue": "2.2.0", "@coreui/utils": "^2.0.2", "@coreui/vue": "^5.4.0", "@formkit/addons": "^1.6.7", "@formkit/themes": "^1.6.7", "@formkit/vue": "^1.6.7", "@types/js-cookie": "^3.0.6", "@vueform/multiselect": "^2.6.10", "bootstrap": "^5.3.3", "bootstrap-vue-next": "^0.25.15", "filepond": "^4.32.1", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-preview": "^4.6.12", "glob": "^11.0.0", "js-cookie": "^3.0.5", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "material-icons": "^1.13.12", "material-symbols": "^0.25.1", "mathjs": "^13.2.2", "moment": "^2.30.1", "pinia": "^2.2.2", "simplebar-vue": "^2.3.5", "sweetalert2": "^11.14.5", "vee-validate": "^4.14.7", "vue": "^3.5.8", "vue-element-loading": "^3.0.1", "vue-filepond": "^7.0.4", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5", "vue-toast-notification": "^3.1.3", "yup": "^1.4.0"}}