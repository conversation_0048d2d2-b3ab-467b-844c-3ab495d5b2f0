<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-1">
				<CCardHeader>
					<div class="d-flex justify-content-start align-items-center">
						<b-dropdown size="lg" variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab">
							<template #button-content>
								<span class="material-icons-outlined">tune</span>
							</template>
							<b-dropdown-item 
								v-for="(tab, index) in optionTabs" 
								:key="index" 
								@click="handleClickTab(tab.value)"
							>
								{{ tab.title }} ({{ dataCounts[tab.value] }})
							</b-dropdown-item>
							<b-dropdown-divider></b-dropdown-divider>
							<b-dropdown-item>
                                <div class="d-inline-flex align-items-center">
                                    <span class="material-symbols-outlined">auto_fix_high</span>
                                    <span class="m-2">{{ $t('menu_tab.custom') }}</span>
                                </div>
                            </b-dropdown-item>
						</b-dropdown>
						<ul class="nav">
							<li v-for="(tab, index) in optionTabs" :key="index" class="nav-item">
								<a 
									:class="{ active: isActiveTab(tab.value) }" 
									class="nav-link"
									@click="handleClickTab(tab.value)"
								>
									{{ tab.title }} ({{ dataCounts[tab.value] }})
								</a>
							</li>
						</ul>
						<div class="ms-auto">
							<CButton
								class="btn btn-light d-flex align-items-center"
								@click="handleClickAddPermission"
							>
								<span class="material-symbols-outlined me-1">add_circle</span>
								<span class="fw-normal">{{ $t('permission.add') }} </span>
							</CButton>
						</div>
					</div>
				</CCardHeader>
			</CCard>
			<CCard class="mb-4">
				<CCardHeader>
					<paginate 
						:meta="paginate.meta" 
						:links="paginate.links" 
						@page="page" 
						@per-page="perPage"
					>
					</paginate>
				</CCardHeader>
				<CCardBody>
					<permission-table
						:dataPermissions="dataPermissions"
						@update-data-paginate="updateDataPaginate"
					>
					</permission-table>
				</CCardBody>
			</CCard>
		</CCol>
	</CRow>

	<!-- Modal for Adding Permission -->
	<CModal
		:visible="state.showAddModal"
		@close="closeAddModal"
		size="lg"
		backdrop="static"
	>
		<CModalHeader>
			<CModalTitle>{{ $t('permission.add') }}</CModalTitle>
		</CModalHeader>
		<CModalBody>
			<permission-add
				@close-modal="closeAddModal"
				@permission-added="handlePermissionAdded"
			/>
		</CModalBody>
	</CModal>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router';
import { useI18n } from "vue-i18n";
import PermissionTable from '@/views/admin/permission/PermissionTable.vue';
import PermissionAdd from '@/views/admin/permission/PermissionAdd.vue';
import usePermissions from '@/composables/permission';
import { PERMISSION_STATUS } from "@/constants/constants";
import Paginate from '@/views/paginate/Paginate.vue';
  
export default defineComponent({
    name: "PermissionListData",

	components: {
		PermissionTable,
		PermissionAdd,
		Paginate
	},
  
    setup() {
		const route = useRoute();
        const { t }  = useI18n();

		const state = reactive({
			tabActived: '' as any,
			paginate: {
				page: 1,
		        perPage: 10,
			},
			showAddModal: false
		});
		
		// Sử dụng computed để optionTabs cập nhật khi ngôn ngữ thay đổi
		const optionTabs = computed(() => [
			{ value: PERMISSION_STATUS.ALL, title: t('option_tab_permission.all') },
            { value: PERMISSION_STATUS.ACTIVE, title: t('option_tab_permission.active') },
			{ value: PERMISSION_STATUS.UNACTIVE, title: t('option_tab_permission.unactive') },
		]);

		const isActiveTab = (valueTabActived: string) => {
			return route.query.tab === valueTabActived;
		};

		const handleClickTab = (valueTabActived: string) => {
			state.tabActived = valueTabActived;
			getAllPermissions(
				state.paginate.page, 
				state.paginate.perPage, 
				state.tabActived
			);
		};

		const { setIsLoading, paginate, dataPermissions, dataCounts, getAllPermissions } = usePermissions();

		const refreshData = (): void => {
			state.tabActived = route.query.tab || PERMISSION_STATUS.ALL;
			getAllPermissions(
				state.paginate.page, 
				state.paginate.perPage,
				state.tabActived
			);
        };
 
        onMounted(() => {
		 	refreshData();
		})

        const page = (page: number): void => {
            getAllPermissions(
				page, 
				state.paginate.perPage,
				state.tabActived
			);
        };

        const perPage = (perPage: number): void => {
            state.paginate.perPage = perPage;
            getAllPermissions(
				state.paginate.page, 
				perPage,
				state.tabActived
			);
        };

		const updateDataPaginate = (value: any): void => {
			getAllPermissions(
				value.currentPage,
				value.perPage,
				state.tabActived
			);
			setIsLoading.value = false;
		}

		const handleClickAddPermission = (): void => {
			state.showAddModal = true;
		}

		const closeAddModal = (): void => {
			state.showAddModal = false;
		}

		const handlePermissionAdded = (): void => {
			refreshData(); // Refresh the permission list after adding
		}

		return {
			state,
			optionTabs,
			setIsLoading,
			paginate,
			isActiveTab,
			handleClickTab,
			dataPermissions,
			dataCounts,
			refreshData,
			page,
			perPage,
			updateDataPaginate,
			handleClickAddPermission,
			closeAddModal,
			handlePermissionAdded,
		}
    }
});
</script>

<style scoped>

</style>