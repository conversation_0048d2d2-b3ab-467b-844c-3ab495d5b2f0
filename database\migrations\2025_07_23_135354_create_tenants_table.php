<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name'); // Tên đơn vị
            $table->string('domain_name')->unique(); // Tên miền
            $table->date('start_date'); // Ngày bắt đầu
            $table->date('end_date')->nullable(); // <PERSON><PERSON>y kết thúc
            $table->boolean('is_active')->default(true); // Trạng thái hoạt động
            $table->uuid('create_by'); // Người tạo
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('tenants', function (Blueprint $table) {
            $table->index(['id', 'domain_name', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};
