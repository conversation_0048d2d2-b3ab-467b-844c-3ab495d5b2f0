<template>
    <CCol :xs="12">
        <FormKit
            ref="formProcessGroup"
            type="form"
            :actions="false"
            incomplete-message=" "
            @submit="handleSubmitFormProcessGroup"
        >
            <FormKit
                label-class="required-label" 
                type="text"
                :label="$t('process_group.name')"
                :floating-label="true"
                v-model="dataProcessGroup.name"
                validation="required|length:1,200"
                :validation-messages="{
                    required: `${$t('process_group.name')} ${$t('process_group.validate.required')}`,
                    length: `${$t('process_group.name')} ${$t('process_group.validate.name_length')}`
                }"
            />
        </FormKit>
        <CCardFooter>
            <div class="d-flex justify-content-end">
                <CButton 
                    type="button"
                    class="btn btn-light border m-1"
                    @click="closeFormProcessGroup"
                >
                    <span class="text-uppercase">
                        {{ $t('process_group.close') }}
                    </span>
                </CButton>
                <CButton 
                    type="button"
                    class="btn btn-primary m-1"
                    @click.prevent="submitFormProcessGroup"
                >
                    <span class="text-uppercase">
                        {{ $t('process_group.save_update') }}
                    </span>
                </CButton>
            </div>
        </CCardFooter>
    </CCol>
    <loading
        :isLoading="setIsLoading"
    />
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import useProcessGroups from '@/composables/processGroup';
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import Loading from '@/views/loading/Loading.vue';

export default defineComponent({
    name: 'ProcessGroupAdd',
    emits: ['close-modal-process-group'],

    components: {
        Loading
    },

    props: {
        dataProcessGroup: {
            type: Object,
            default: {},
            required: true,
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const formProcessGroup: any = ref(null);

        const submitFormProcessGroup = () => {
			const node = formProcessGroup.value.node
			node.submit()
		}

        const closeFormProcessGroup = () => {
            emit('close-modal-process-group');
		}

        const { setIsLoading, storeProcessGroup } = useProcessGroups();
       
        const handleSubmitFormProcessGroup = async () => {
            let formProcessGroupData = new FormData();
            formProcessGroupData.append('formProcessGroup', JSON.stringify(props.dataProcessGroup));
            let response: any = await storeProcessGroup(formProcessGroupData);

            if (response) {
                if (response.status === 'success') {
                    emit('close-modal-process-group');
                    $toast.open({
                        message: t('toast.status.ACTION_SUCCESS'),
                        type: "success",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });
                }
            }
        }

        return {
            setIsLoading,
            formProcessGroup,
            submitFormProcessGroup,
            closeFormProcessGroup,
            handleSubmitFormProcessGroup,
        }
    },
});
</script>
<style type="text/css" scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color:#f8f9fa;
    padding: 10px;
}
</style>