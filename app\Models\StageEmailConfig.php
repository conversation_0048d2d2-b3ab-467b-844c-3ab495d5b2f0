<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class StageEmailConfig extends Model
{
    use HasUuids;
    
    protected $table = 'stage_email_configs';
    protected $primaryKey = 'id';

    protected $fillable = [
        'stage_id',
        'template_id',
        'action_id',
        'process_version_id',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function emailConditions()
    {
        return $this->hasMany('App\Models\EmailCondition', 'stage_email_config_id', 'id');
    }

    /**
     * <PERSON><PERSON><PERSON> quan hệ với bảng email_templates
     */
    public function template()
    {
        return $this->belongsTo('App\Models\EmailTemplate', 'template_id', 'id');
    }

    /**
     * <PERSON><PERSON><PERSON> quan hệ với bảng actions
     */
    public function action()
    {
        return $this->belongsTo('App\Models\Action', 'action_id', 'id');
    }
}
