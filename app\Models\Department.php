<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use \Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;
use App\Traits\ActiveGlobalScopeTrait;

class Department extends BaseModel
{
    use HasRecursiveRelationships, ActiveGlobalScopeTrait, HasUuids;
    protected $table = 'departments';
    protected $primaryKey = 'id';

    protected $fillable = [
        'object',
        'code',
        'name',
        'description',
        'parent_id',
        'type_department_id',
        'tax_code',
        'date_of_establish',
        'head_office',
        'address',
        'order',
        'is_active',
        'tenant_id',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function typeDepartment()
    {
        return $this->belongsTo('App\Models\TypeDepartment', 'type_department_id', 'id');
    }
}

