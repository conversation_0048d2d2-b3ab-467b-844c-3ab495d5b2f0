<?php

namespace App\Services;

class DataProcessingService
{
    /**
     * Trích xuất các ID value được nhóm
     */
    public function extractValueIdsGrouped(array $array) 
    {
        $value_ids_grouped = [];
    
        foreach ($array as $subArray) {
            $value_ids = [];
    
            // Kiểm tra nếu phần tử là mảng và chứa các mảng con
            if (is_array($subArray)) {
                // Kiểm tra xem phần tử có phải là mảng đơn hay mảng đa chiều
                if (isset($subArray[0]) && is_array($subArray[0])) {
                    // Mảng đa chiều
                    foreach ($subArray as $item) {
                        if (is_array($item) && isset($item['value'])) {
                            $value_ids[] = $item['value'];
                        }
                    }
                } else {
                    // Mảng đơn hoặc mảng không lồng
                    if (isset($subArray['value'])) {
                        $value_ids[] = $subArray['value'];
                    }
                }
    
                if (!empty($value_ids)) {
                    $value_ids_grouped[] = $value_ids;
                }
            }
        }
    
        return $value_ids_grouped;
    }

    /**
     * Trích xuất các label được nhóm
     */
    public function extractLabelsGrouped(array $array)
    {
        $labels_grouped = [];
    
        foreach ($array as $subArray) {
            $labels = [];
    
            // Kiểm tra nếu phần tử là mảng và chứa các mảng con
            if (is_array($subArray)) {
                // Kiểm tra xem phần tử có phải là mảng đơn hay mảng đa chiều
                if (isset($subArray[0]) && is_array($subArray[0])) {
                    // Mảng đa chiều
                    foreach ($subArray as $item) {
                        if (is_array($item) && isset($item['label'])) {
                            $labels[] = $item['label'];
                        }
                    }
                } else {
                    // Mảng đơn hoặc mảng không lồng
                    if (isset($subArray['label'])) {
                        $labels[] = $subArray['label'];
                    }
                }
    
                if (!empty($labels)) {
                    $labels_grouped[] = $labels;
                }
            }
        }
    
        return $labels_grouped;
    }

    /**
     * Trích xuất tất cả các ID value theo đối tượng
     */
    public function extractAllValueIdsByObject(array $array)
    {
        $value_ids = [];
    
        foreach ($array as $item) {
            if (is_array($item)) {
                if (is_array($item) && isset($item['value'])) {
                    $value_ids[] = $item['value'];
                }
                // Gọi đệ quy cho các phần tử con
                $value_ids = array_merge($value_ids, $this->extractAllValueIdsByObject($item));
            }
        }
    
        // Loại bỏ các giá trị trùng lặp
        $value_ids = array_unique($value_ids);
    
        return $value_ids;
    }
} 