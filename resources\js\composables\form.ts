import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";

export default function useForms() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const getFieldsByFormId = async (valueFormId: string, valueStageId: string) => {
        try {
            let response = await axios.get('/api/form-fields', {
				params: {
				    formId: valueFormId,
                    stageId: valueStageId,
				}
			});

            return response?.data;
        } catch (error: any) {
            console.log(error);
            if (!error.response) {
                setTimeout(() => {
                    getFieldsByFormId(valueFormId, valueStageId);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    return {
        setIsLoading,
        getFieldsByFormId,
    }
}