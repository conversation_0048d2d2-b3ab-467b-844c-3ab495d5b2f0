import { createI18n } from 'vue-i18n'
import viMessage from './vi';
import enMessage from './en';
import  { checkLocale } from "@/utils/utils";

const messages = {
    vi: viMessage,
    en: enMessage,
}

// Use app_language from localStorage which is set during login
let locale = checkLocale(localStorage.getItem('app_language'));

const i18n = createI18n({
    legacy: false as any,
    locale: locale,
    fallbackLocale: 'vi',
    messages,
});

export default i18n;

