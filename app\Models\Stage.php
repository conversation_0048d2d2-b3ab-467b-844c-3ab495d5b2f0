<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Stage extends Model
{
    use HasUuids;
    
    protected $table = 'stages';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'approver',
        'followers',
        'comment',
        'process_version_id',
    ];
    
    protected $casts = [
        'comment' => 'boolean',
        'approver' => 'array',
        'followers' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
