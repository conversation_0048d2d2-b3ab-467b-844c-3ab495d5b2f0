<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('process_instance_stage_status', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('job_id');  // ID công việc
            $table->uuid('stage_id');  // ID giai đoạn
            $table->string('status', 10);  // Trạng thái xử lý
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('process_instance_stage_status', function (Blueprint $table) {
            $table->index(['id', 'job_id', 'stage_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('process_instance_stage_status');
    }
};
