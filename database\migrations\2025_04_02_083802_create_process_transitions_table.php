<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('process_transitions', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('name', 250);  // Tên công việc xử lý tiếp
            $table->string('slug', length: 250);  // Slug công việc xử lý tiếp
            $table->uuid('from_stage_id');  // ID giai đoạn bắt đầu
            $table->uuid('to_process_id');  // ID quy trình chuyển tiếp
            $table->string('type_create', 10);  // Loại quy trình chuyển tiếp
            $table->uuid('action_id');  // ID hành động
            $table->uuid('process_version_id');  // Thuộc phiên bản quy trình
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('process_transitions', function (Blueprint $table) {
            $table->index(['id', 'process_version_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('process_transitions');
    }
};
