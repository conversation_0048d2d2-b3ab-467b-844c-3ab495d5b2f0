<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stage_sync_groups', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('stage_id');  // ID giai đoạn
            $table->uuid('sync_group_id');  // ID nhóm đồng bộ
            $table->uuid('action_id');  // ID hành động
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('stage_sync_groups', function (Blueprint $table) {
            $table->index(['id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stage_sync_groups');
    }
};
