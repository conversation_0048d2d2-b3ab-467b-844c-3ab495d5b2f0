<script setup>
	import { onMounted, ref } from 'vue'
	import router from '@/router'
	import { resolveRouteTitle } from '@/router/titleResolver'
	import { languageEventBus } from '@/utils/languageUtils'

	const breadcrumbs = ref('')

	const getBreadcrumbs = () => {
		const titleKey = router.currentRoute.value.meta.titleKey;
		return titleKey ? resolveRouteTitle(titleKey) : '';
	}

	router.afterEach(() => {
		breadcrumbs.value = getBreadcrumbs()
	})

	// Cập nhật breadcrumb khi ngôn ngữ thay đổi
	languageEventBus.onLanguageChanged(() => {
		breadcrumbs.value = getBreadcrumbs()
	})

	onMounted(() => {
		breadcrumbs.value = getBreadcrumbs()
	})
</script>

	<template>
	<CBreadcrumb class="my-0">
		<CCardHeader>
			<span>{{ breadcrumbs }}</span>
		</CCardHeader>
	</CBreadcrumb>
</template>