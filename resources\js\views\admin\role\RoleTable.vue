<template>
    <CTable align="middle" responsive>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th class="align-middle">
                        {{ $t('role.name') }}
                    </th>
                    <th class="align-middle text-center">
                        {{ $t('role.is_admin') }}
                    </th>
                    <th class="align-middle text-center">
                        {{ $t('role.user_active') }}
                    </th>
                    <th class="align-middle text-center">
                        {{ $t('role.user_block') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('role.create_by') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('role.update_by') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('role.created_at') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('role.update_at') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('role.expired_at') }}
                    </th>
                </tr>
            </thead>
            <tbody v-if="checkDataNotEmpty">
                <tr v-for="(role, index) in dataRoles" :key="index">
                    <td class="align-middle">
                        {{ role.name }}
                    </td>
                    <td class="align-middle text-center">
                        {{ role.is_admin ? $t('role.is_admin_yes') : $t('role.is_admin_no') }}
                    </td>
                    <td class="align-middle text-center">
                        {{ role.active_users_count }}
                    </td>
                    <td class="align-middle text-center">
                        {{ role.inactive_users_count }}
                    </td>
                    <td class="align-middle">
                        {{ role?.create_by?.full_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ role?.update_by?.full_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ role.created_at ? formatDate(role.created_at) : $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ role.updated_at ? formatDate(role.updated_at) : $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ role.expired_at ? formatDate(role.expired_at) : $t('common.no_data') }}
                    </td>
                </tr>
            </tbody>
            <tbody v-else>
                <tr>
                    <td colspan="9" class="align-middle text-center">
                        {{ $t('search.no_matching_records_found') }}
                    </td>
                </tr>
            </tbody>
        </table>
    </CTable>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import moment from 'moment'

export default defineComponent({
    name: 'RoleTable',
    emits: ['update-data-paginate'],

    props: {
        dataRoles: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const checkDataNotEmpty = computed<boolean>(() => {
            if (props.dataRoles.length == 0) {

                return false;
            } else {

                return true;
            }
        });
        const formatDate = (date: string) => {
            return moment(date).format('DD/MM/YYYY');
        };

        return {
            checkDataNotEmpty,
            formatDate,
        }
    },
});
</script>
<style type="text/css" scoped>
</style>