<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permission_role', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('permission_id');
            $table->uuid('role_id');
            $table->string('scope', 20);
            $table->timestamps();
            // Mỗi vai trò chỉ có một quyền duy nhất một lần
            $table->unique(['permission_id', 'role_id']);
        });

        // Tạo chỉ mục
        Schema::table('permission_role', function (Blueprint $table) {
            $table->index(['id', 'permission_id', 'role_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permission_role');
    }
};
