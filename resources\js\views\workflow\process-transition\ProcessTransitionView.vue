<template>
    <div v-if="processTransitions && processTransitions.length">
        <div v-for="(pt, ptIdx) in processTransitions" :key="ptIdx">
            <CForm class="row g-2">
                <CCol :md="6" class="mb-3">
                    <CFormLabel class="text-secondary">
                        {{ $t('workflow.process_transition.name')}}
                    </CFormLabel>
                    <div class="fw-normal">
                        {{ pt?.name || $t('common.no_data') }}
                    </div>
                </CCol>
                <CCol :md="6" class="mb-3">
                    <CFormLabel class="text-secondary">
                        {{ $t('workflow.process_transition.apply_process')}}
                    </CFormLabel>
                    <div class="fw-normal">
                        {{ pt?.to_process_id || $t('common.no_data') }}
                    </div>
                </CCol>
                <CCol :md="6" class="mb-3">
                    <CFormLabel class="text-secondary">
                        {{ $t('workflow.process_transition.type_create')}}
                    </CFormLabel>
                    <div class="fw-normal">
                        {{ pt?.type_create === 'manual' ? $t('workflow.process_transition.type_create_manual') : $t('workflow.process_transition.type_create_auto') }}
                    </div>
                </CCol>
                <CCol :md="6" class="mb-3">
                    <CFormLabel class="text-secondary">
                        {{ $t('workflow.process_transition.condition')}}
                    </CFormLabel>
                    <div class="fw-normal">
                        <div v-if="pt.condition && pt.condition.length">
                            <div 
                                v-for="(condition, ptcIdx) in pt.condition" 
                                :key="`ptc-${ptcIdx}`" 
                            >
                                {{ condition.name }}
                            </div>
                        </div>
                    </div>
                </CCol>
            </CForm>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'

export default defineComponent({
	name: 'ProcessTransitionView',
	
	props: {
		processTransitionData: {
			type: Object,
			required: true
		}
	},

	setup(props) {
		// Computed property to get process transitions from data
		const processTransitions = computed(() => {
			return props.processTransitionData?.process_transition || []
		})

		return {
			processTransitions,
		}
	}
})
</script>

<style scoped>

</style>
