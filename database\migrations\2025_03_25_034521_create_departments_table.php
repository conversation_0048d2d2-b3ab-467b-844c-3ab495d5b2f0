<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('departments', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('object', 50);  // Đối tượng: Công ty, chi nhánh, phòng ban
            $table->string('code', 50)->nullable();  // Mã phòng ban/ Tên viết tắt
            $table->string('name', 255);  // Tên phòng ban / Tên công ty / Tên chi nhánh - tiêu đề
            $table->string('description', 500)->nullable();  // Mô tả phòng ban / công ty/ chi nhánh
            $table->uuid('parent_id')->nullable(); // Thuộc phòng ban/ thuộc công ty
            $table->uuid('type_department_id')->nullable(); // Thuộc loại phòng ban (Khối/phòng/bộ phận/ nhóm)
            $table->string('tax_code', 50)->nullable();  // Mã số thuế
            $table->date('date_of_establish')->nullable();  // Ngày thành lập
            $table->string('head_office', 200)->nullable();  // Trụ sở chính
            $table->string('address', 255)->nullable();  // Địa chỉ
            $table->integer('order')->nullable(); // Thứ tự sắp xếp lên xuống với đối tượng cùng cấp
            $table->boolean('is_active')->default(true)->comment('0 - Không hoạt động, 1 - Hoạt động');  // Trạng thái hoạt động
            $table->uuid('tenant_id');  // Thuộc đơn vị
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('departments', function (Blueprint $table) {
            $table->index(['id', 'parent_id', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
