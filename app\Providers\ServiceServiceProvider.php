<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\ProcessConditionService;
use App\Services\StageTransitionService;
use App\Services\EmailService;
use App\Services\JobApprovalService;
use App\Services\DataProcessingService;
use App\Services\WorkflowProcessService;
use App\Services\FormDataProcessingService;
use App\Services\FileUploadService;
use App\Services\JobCreationService;
use App\Services\JobPermissionService;

class ServiceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Đăng ký các service độc lập
        $this->app->singleton(ProcessConditionService::class);
        $this->app->singleton(DataProcessingService::class);
        $this->app->singleton(FileUploadService::class);
        $this->app->singleton(JobPermissionService::class);
        
        // Đăng ký các service phụ thuộc
        $this->app->singleton(StageTransitionService::class);
        $this->app->singleton(EmailService::class);
        $this->app->singleton(JobApprovalService::class);
        $this->app->singleton(FormDataProcessingService::class);
        $this->app->singleton(JobCreationService::class);
        $this->app->singleton(WorkflowProcessService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
} 