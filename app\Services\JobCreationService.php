<?php

namespace App\Services;

use App\Models\ProcessVersion;
use App\Models\User;
use App\Repositories\SaveJob\SaveJobRepositoryInterface;
use App\Repositories\User\UserRepositoryInterface;

class JobCreationService
{
    protected $saveJobRepository;
    protected $userRepository;
    protected $fileUploadService;

    public function __construct(
        SaveJobRepositoryInterface $saveJobRepository,
        UserRepositoryInterface $userRepository,
        FileUploadService $fileUploadService
    ) {
        $this->saveJobRepository = $saveJobRepository;
        $this->userRepository = $userRepository;
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Tạo job mới
     */
    public function createJob($form_data_job, $current_user, $file_uploads = null)
    {
        if (!$form_data_job) {
            return null;
        }

        $form_data_job = is_string($form_data_job) ? json_decode($form_data_job, true) : $form_data_job;
        $process_id = $form_data_job['workflow']['value'];
        
        $process_version = $this->validateProcess($process_id);
        if (!$process_version) {
            return null;
        }

        $process_version_id = $process_version->id;
        $is_multiple = $form_data_job['is_multiple'] ?? false;
        $status_job = 'pending'; // Trạng thái chờ xử lý
        
        // Xử lý file_uploads
        $file_name_news = null;
        if (!empty($file_uploads)) {
            $file_name_news = $this->fileUploadService->uploadFiles($file_uploads, 'file_uploads');
        }
        
        // Lấy danh sách người theo dõi
        $followers = $this->userRepository->getUserByOptionScopeRes(array_column($form_data_job['followers'] ?? [], 'value'));
        
        // Tạo dữ liệu lưu vào bảng save_jobs
        $data_save_job = [
            'name' => $form_data_job['name'],
            'user_id' => $current_user->id,
            'department_id' => $current_user->department_id,
            'job_position_id' => $current_user->job_position_id,
            'process_version_id' => $process_version_id,
            'status' => $status_job,
            'description' => $form_data_job['description'] ?? null,
            'files' => $file_name_news,
            'followers' => !empty($followers) ? array_column($followers, 'value') : null,
            'managers' => !empty($form_data_job['job_manager']) ? array_column($form_data_job['job_manager'], 'value') : null,
            'create_by' => $current_user->id,
        ];

        return $this->saveJobRepository->create($data_save_job);
    }

    /**
     * Xác thực và lấy thông tin phiên bản quy trình
     */
    public function validateProcess($process_id)
    {
        if (!$process_id) {
            return null;
        }

        return ProcessVersion::processVersionActive($process_id)->first();
    }
} 