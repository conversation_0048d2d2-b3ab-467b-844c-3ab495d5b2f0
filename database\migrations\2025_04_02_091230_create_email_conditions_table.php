<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_conditions', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('stage_email_config_id');  // ID cấu hình email giai đoạn
            $table->uuid('condition_id');  // ID điều kiện áp dụng
            $table->string('condition_status', 5)->comment('true - Thành công, false - Thất bại');  // Trạng thái điều kiện
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('email_conditions', function (Blueprint $table) {
            $table->index(['id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_conditions');
    }
};
