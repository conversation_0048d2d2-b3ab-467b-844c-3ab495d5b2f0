import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";

export default function useFields() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const getTables = async () => {
        try {
            let response = await axios.get('/api/tables');

            return response?.data;
        } catch (error: any) {
            console.log(error);
            if (!error.response) {
                setTimeout(() => {
                    getTables();
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getColumns = async (valueTable: string) => {
        try {
            let response = await axios.get('/api/columns', {
				params: {
					table: valueTable,
				}
			});

            return response?.data;
        } catch (error: any) {
            console.log(error);
            if (!error.response) {
                setTimeout(() => {
                    getColumns(valueTable);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getColumnDatas = async (valueObjectTable: string, valueSubColumnTable: string, valueColumnTable: string, valueQuery: string) => {
        try {
            let response = await axios.get('/api/column-data', {
				params: {
					object_table: valueObjectTable,
                    sub_column_table: valueSubColumnTable,
                    column_table: valueColumnTable,
                    query: valueQuery,
				}
			});

            return response?.data;
        } catch (error: any) {
            console.log(error);
            if (!error.response) {
                setTimeout(() => {
                    getColumnDatas(valueObjectTable, valueSubColumnTable, valueColumnTable, valueQuery);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const storeField = async (dataField: object) => {
        setIsLoading.value = true;
        try {
            return await axios.post(`/api/fields`, dataField);
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storeField(dataField);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    return {
        setIsLoading,
        getTables,
        getColumns,
        getColumnDatas,
        storeField,
    }
}