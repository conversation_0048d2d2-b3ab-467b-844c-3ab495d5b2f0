<?php
namespace App\Repositories\JobApprovalHistory;

use App\Repositories\EloquentRepository;

class JobApprovalHistoryRepository extends EloquentRepository implements JobApprovalHistoryRepositoryInterface
{
	public function getModel()
	{
		return \App\Models\JobApprovalHistory::class;
	}
	
	/**
	 * L<PERSON>y lịch sử phê duyệt của một công việc
	 */
	public function getApprovalHistoriesForJob($jobId)
	{
		return $this->model
			->where('job_id', $jobId)
			->with(['actor'])
			->orderBy('created_at', 'asc')
			->get();
	}
	
	/**
	 * Kiểm tra người dùng có quyền phê duyệt một lịch sử cụ thể không
	 */
	public function canUserApprove($userId, $approvalHistoryId)
	{
		$approvalHistory = $this->find($approvalHistoryId);
		
		if (!$approvalHistory) {
			return false;
		}
		
		// Kiểm tra người dùng có trong danh sách người duyệt không
		return in_array($userId, $approvalHistory->approved_list ?? []);
	}
	
	/**
	 * Lấy các công việc đang chờ người dùng phê duyệt
	 */
	public function getPendingApprovalsForUser($userId, $dataSearch)
	{
		$page = isset($dataSearch['page']) ? $dataSearch['page'] : null;
		$per_page = isset($dataSearch['perPage']) ? $dataSearch['perPage'] : null;
		
		$query = $this->model
			->whereJsonContains('approved_list', $userId)
			->whereNull('user_id') // Chưa được phê duyệt (chưa có người xử lý)
			->with(['job'])
			->orderBy('created_at', 'desc');
			
		if (isset($page) && isset($per_page)) {
			$approvals = $query->paginate($per_page, ['*'], 'page', $page);
		} else {
			$approvals = $query->get();
		}
		
		return $approvals;
	}
	
	/**
	 * Kiểm tra người dùng có nằm trong quy trình phê duyệt của công việc không
	 */
	public function isUserInApprovalProcess($userId, $jobId)
	{
		// Kiểm tra xem người dùng có trong danh sách phê duyệt của bất kỳ lịch sử nào không
		$count = $this->model
			->where('job_id', $jobId)
			->whereJsonContains('approved_list', $userId)
			->count();
			
		// Hoặc người dùng là người theo dõi lịch sử phê duyệt
		$countFollower = $this->model
			->where('job_id', $jobId)
			->whereJsonContains('followers', $userId)
			->count();
			
		return ($count > 0 || $countFollower > 0);
	}
}

?>