<template>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';
import { useAuthStore } from '@/stores/auth';

export default defineComponent({
    name: 'AuthMiddleware',
    
    setup() {
        const authStore = useAuthStore();
        const checkInterval = ref<number | null>(null);
        
        // Check for permission updates every 2 minutes
        const startPermissionChecker = () => {
            checkInterval.value = window.setInterval(async () => {
                if (authStore.isAuthenticated) {
                    await authStore.checkPermissionsUpdate();
                }
            }, 60 * 60 * 1000); // 1 hour
        };
        
        onMounted(() => {
            // Only start the permission checker if already authenticated
            // Let the router guard handle authentication initialization
            if (authStore.isAuthenticated) {
                startPermissionChecker();
            }
        });
        
        onUnmounted(() => {
            // Clear interval when component is unmounted
            if (checkInterval.value) {
                clearInterval(checkInterval.value);
            }
        });
        
        return {};
    }
});
</script>
