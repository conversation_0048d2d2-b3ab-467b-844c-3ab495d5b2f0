<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\App;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Lấy ngôn ngữ từ header 'Accept-Language', hoặc mặc định là 'vi'
        $locale = $request->header('Accept-Language', 'vi');
        // Kiểm tra ngôn ngữ có được hỗ trợ không
        if (in_array($locale, ['en', 'vi'])) {
            App::setLocale($locale);
        } else {
            App::setLocale('vi'); // Ngôn ngữ mặc định
        }

        return $next($request);
    }
}
