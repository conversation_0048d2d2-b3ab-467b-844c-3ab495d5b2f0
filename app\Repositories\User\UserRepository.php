<?php
namespace App\Repositories\User;

use App\Repositories\EloquentRepository;
use Illuminate\Support\Str;
use App\Models\Department;
use App\Models\DepartmentSub;
use App\Models\Rank;
use App\Models\JobPosition;
use App\Models\SaveJob;
use App\Models\Process;
use Carbon\Carbon;
use App\Enums\UserStatus;
use Illuminate\Support\Facades\Auth;

class UserRepository extends EloquentRepository implements UserRepositoryInterface
{
    public function getModel()
    {
        return \App\Models\User::class;
    }

    public function getAllUsers($dataSearch)
	{
		// Chuyển đổi tab thành enum PermissionStatus
		$tabStatus = isset($dataSearch['tab']) && !empty($dataSearch['tab']) 
			? UserStatus::fromString($dataSearch['tab']) 
			: UserStatus::ALL;

        $page = $dataSearch['page'] ?? null;
        $perPage = $dataSearch['perPage'] ?? null;
		
		$select_columns = [
			'id',
			'account_name',	
			'full_name',
			'email',
			'department_id',
			'rank_id',
			'job_position_id',
			'is_active',
            'not_activated',
			'create_by',
            'created_at',
            'roles_updated_at',
		];
		
		$query = $this->model
			->with([
                'department:id,name,is_active', 
                'rank:id,name,is_active', 
                'jobPosition:id,name,is_active', 
                'createBy:id,full_name',
                'roles:id,name',
            ])
			->select($select_columns);

        // Tạo baseQuery để sử dụng cho việc đếm các tab
        $baseQuery = clone $query;

        // Áp dụng điều kiện theo tab
        $tabStatus->applyToQuery($query);
        
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';
        $query->orderBy($orderBy, $orderDirection);
        
        // Lấy danh sách trạng thái từ enum
        $statusList = UserStatus::cases();
        $counts = [];
        foreach ($statusList as $status) {
            $countQuery = clone $baseQuery;
            $status->applyToQuery($countQuery);
            $counts[strtolower($status->name)] = $countQuery->count();
        }

        if ($page && $perPage) {
            $users = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $users = $query->get();
        }
        
        return [
            'users' => $users,
            'counts' => $counts
        ];
	}

	public function getUserByOptionScopeRes($option_scopes, $job_id = null, $stage_ids = null)
	{
		// Sau khi bóc tách được id
        // Sẽ check switch theo từng type
        // Type là U thì sẽ where với Model User
        // Type là D thì sẽ where với Model Department từ bảng này tôi sẽ check xem id này có các phần cấp con không thông qua cột parent_id trong bảng này, sau khi lấy được tất cả các id cấp con đó tôi sẽ query với bảng User để lấy ra tất cả các user
        // Type là R thì sẽ where với Model Rank để lấy id_rank sau đó lấy id này where với Model User để lấy tất cả user có rank này
        // Type là RD thì sẽ where với Model Rank để lấy id_rank sau đó lấy id này where với Model User để lấy tất cả user có rank này và có department_id = department_id của user hiện tại
        $prefixes = config('constants.prefixes'); // Lấy các prefix dùng chung
        $processed_results = collect($option_scopes)->map(function ($item) use ($prefixes) {
            // Duyệt qua bản đồ Loại => Tiền tố (đã được sắp xếp)
            foreach ($prefixes as $type => $prefixString) {
                // Kiểm tra xem $item có bắt đầu bằng $prefixString không
                if (Str::startsWith($item, $prefixString)) {
                    // Nếu có, dùng Str::after để lấy phần sau $prefixString
                    $value = Str::after($item, $prefixString);

                    // Trả về kết quả cho item này và dừng kiểm tra các tiền tố khác
                    return [
                        'type'  => $type,  // Loại tìm được (key từ $prefixMap)
                        'value' => $value  // Giá trị đã bóc tách
                    ];
                }
            }

            // Nếu vòng lặp kết thúc mà không tìm thấy tiền tố nào khớp
            return [
                'type'  => 'OPTION_SYSTEM_DEFAULT', // Đánh dấu là option của hệ thống
                'value' => $item  // Giữ nguyên giá trị gốc trong trường hợp này
            ];
        });

        $processed_results = $processed_results->all();

        // --- Phần 2: Lặp qua kết quả và thực hiện hành động theo type ---
        // Sử dụng 2 mảng riêng cho kết quả:
        // - $approve_groups chứa các group từ approve_by_id
        // - $others chứa các kết quả của các trường hợp khác (user_id, create_by_id,...)
        $approve_groups = [];
        $others = [];

        foreach ($processed_results as $result) {
            $type  = $result['type'];
            $value = $result['value'];

            $retrieved_data = []; // Biến lưu trữ dữ liệu lấy được từ DB hoặc xử lý khác

            switch ($type) {
                case 'U':
                    // --- Xử lý cho type 'U': Tìm User ---
                    // Sử dụng Eloquent để tìm User theo ID (giả định $value là ID)
                    // $this->model::find($value) trả về model User nếu tìm thấy, hoặc null nếu không.
                    $current_user = $this->model::find($value);
                    if ($current_user) {
                        // Tìm thấy User
                        $retrieved_data = [
                            'label' => $current_user->account_name . ' - ' .$current_user->full_name,
                            'value' => $current_user->id, // Bản gốc
                            'email' => $current_user->email
                        ];
                    }

                    break;
                case 'D':
                    // --- Xử lý cho type 'D'
                    $department = Department::find($value);
                    $department_descendant_ids = [];
                    
                    if ($department) {
                        // Lấy tất cả các ID của department và các cấp con
                        $department_descendant_ids = $department->descendantsAndSelf()->pluck('id')->all();
                
                        if (!empty($department_descendant_ids)) {
                            $columns = 'department_id';
                            $values = $department_descendant_ids;
                            // Lấy tất cả user từ bảng User dựa trên department_id
                            $users = $this->model::whereIn($columns, $values)->get();
                
                            // Định dạng dữ liệu trả về từ bảng User
                            $retrieved_data = $users->map(function ($user) {
                                return [
                                    'label' => $user->account_name . ' - ' . $user->full_name,
                                    'value' => $user->id,
                                    'email' => $user->email
                                ];
                            })->values()->toArray();

                            $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                
                            // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                            $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                        }
                    }
                    break;
                case 'R':
                    // --- Xử lý cho type 'R'
                    $rank = Rank::find($value);

                    if ($rank) {
                        $rank_ids = [$rank->id];
                        $users = $this->model::whereIn('rank_id', $rank_ids)->get();
                        // Định dạng dữ liệu trả về từ bảng User
                        $retrieved_data = $users->map(function ($user) {
                            return [
                                'label' => $user->account_name . ' - ' . $user->full_name,
                                'value' => $user->id,
                                'email' => $user->email
                            ];
                        })->values()->toArray();

                        $columns = 'rank_id';
                        $values = $rank_ids;

                        $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    
                        // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                        $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    }
                    break;
                case 'RD':
                    // --- Xử lý cho type 'RD'
                    $current_user = Auth::user();
                    $user_department = $current_user->department()->first();
                    $department_ancestor_ids = $user_department->ancestorsAndSelf()->where('object', '=', 'department')->pluck('id')->all(); // Lấy tất cả các ID của Model Department
                    $columns = ['rank_id', 'department_id'];
                    $rank_ids = [$value];
                    $department_ids = $department_ancestor_ids;
                    $values = [$rank_ids, $department_ids];
                    $users = $this->model::whereIn('department_id', $department_ids)
                        ->whereIn('rank_id', $rank_ids)
                        ->get();
                    // Định dạng dữ liệu trả về từ bảng User
                    $retrieved_data = $users->map(function ($user) {
                        return [
                            'label' => $user->account_name . ' - ' . $user->full_name,
                            'value' => $user->id,
                            'email' => $user->email
                        ];
                    })->values()->toArray();

                    $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                    $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    break;
                case 'RB':
                    // --- Xử lý cho type 'RB'
                    $current_user = Auth::user();
                    $user_department = $current_user->department()->first();
                    $ancestor_ids = $user_department->ancestors()->where('object', '=', 'branch')->pluck('id')->all(); // Lấy tất cả các ID của Model Department
                    $descendant_ids = Department::whereIn('id', $ancestor_ids)->with('descendantsAndSelf')->get()->pluck('descendantsAndSelf.*.id')->flatten()->unique()->toArray();
                    $columns = ['rank_id', 'department_id'];
                    $rank_ids = [$value];
                    $department_ids = $descendant_ids;
                    $values = [$rank_ids, $department_ids];
                    $users = $this->model::whereIn('department_id', $department_ids)
                        ->whereIn('rank_id', $rank_ids)
                        ->get();
                    // Định dạng dữ liệu trả về từ bảng User
                    $retrieved_data = $users->map(function ($user) {
                        return [
                            'label' => $user->account_name . ' - ' . $user->full_name,
                            'value' => $user->id,
                            'email' => $user->email
                        ];
                    })->values()->toArray();
                    
                    $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                    $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    break;
                case 'RC':
                    // --- Xử lý cho type 'RC'
                    $current_user = Auth::user();
                    $user_department = $current_user->department()->first();
                    $ancestor_ids = $user_department->ancestors()->where('object', '=', 'company')->pluck('id')->all(); // Lấy tất cả các ID của Model Department
                    $descendant_ids = Department::whereIn('id', $ancestor_ids)->with('descendantsAndSelf')->get()->pluck('descendantsAndSelf.*.id')->flatten()->unique()->toArray();
                    $columns = ['rank_id', 'department_id'];
                    $rank_ids = [$value];
                    $department_ids = $descendant_ids;
                    $values = [$rank_ids, $department_ids];
                    $users = $this->model::whereIn('department_id', $department_ids)
                        ->whereIn('rank_id', $rank_ids)
                        ->get();
                    // Định dạng dữ liệu trả về từ bảng User
                    $retrieved_data = $users->map(function ($user) {
                        return [
                            'label' => $user->account_name . ' - ' . $user->full_name,
                            'value' => $user->id,
                            'email' => $user->email
                        ];
                    })->values()->toArray();

                    $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                    $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    break;
                case 'JP':
                    // --- Xử lý cho type 'JP'
                    $job_position = JobPosition::find($value);
                    if ($job_position) {
                        $job_position_ids = [$job_position->id];
                        $users = $this->model::whereIn('job_position_id', $job_position_ids)->get();
                        // Định dạng dữ liệu trả về từ bảng User
                        $retrieved_data = $users->map(function ($user) {
                            return [
                                'label' => $user->account_name . ' - ' . $user->full_name,
                                'value' => $user->id,
                                'email' => $user->email
                            ];
                        })->values()->toArray();

                        $columns = 'job_position_id';
                        $values = $job_position_ids;
                        
                        $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    
                        // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                        $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    }
                    break;
                case 'JPD':
                    // --- Xử lý cho type 'JPD'
                    $current_user = Auth::user();
                    $user_department = $current_user->department()->first();
                    $department_ancestor_ids = $user_department->ancestorsAndSelf()->where('object', '=', 'department')->pluck('id')->all(); // Lấy tất cả các ID của Model Department
                    $columns = ['job_position_id', 'department_id'];
                    $job_position_ids = [$value];
                    $department_ids = $department_ancestor_ids;
                    $values = [$job_position_ids, $department_ids];
                    $users = $this->model::whereIn('department_id', $department_ids)
                        ->whereIn('job_position_id', $job_position_ids)
                        ->get();
                    // Định dạng dữ liệu trả về từ bảng User
                    $retrieved_data = $users->map(function ($user) {
                        return [
                            'label' => $user->account_name . ' - ' . $user->full_name,
                            'value' => $user->id,
                            'email' => $user->email
                        ];
                    })->values()->toArray();
                    
                    $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                    $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    break;
                case 'JPB':
                    // --- Xử lý cho type 'JPB'
                    $current_user = Auth::user();
                    $user_department = $current_user->department()->first();
                    $ancestor_ids = $user_department->ancestors()->where('object', '=', 'branch')->pluck('id')->all(); // Lấy tất cả các ID của Model Department
                    $descendant_ids = Department::whereIn('id', $ancestor_ids)->with('descendantsAndSelf')->get()->pluck('descendantsAndSelf.*.id')->flatten()->unique()->toArray();
                    $columns = ['job_position_id', 'department_id'];
                    $job_position_ids = [$value];
                    $department_ids = $descendant_ids;
                    $values = [$job_position_ids, $department_ids];
                    $users = $this->model::whereIn('department_id', $department_ids)
                        ->whereIn('job_position_id', $job_position_ids)
                        ->get();
                    // Định dạng dữ liệu trả về từ bảng User
                    $retrieved_data = $users->map(function ($user) {
                        return [
                            'label' => $user->account_name . ' - ' . $user->full_name,
                            'value' => $user->id,
                            'email' => $user->email
                        ];
                    })->values()->toArray();

                    
                    $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                    $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    break;
                case 'JPC':
                    // --- Xử lý cho type 'JPC'
                    $current_user = Auth::user();
                    $user_department = $current_user->department()->first();
                    $ancestor_ids = $user_department->ancestors()->where('object', '=', 'company')->pluck('id')->all(); // Lấy tất cả các ID của Model Department
                    $descendant_ids = Department::whereIn('id', $ancestor_ids)->with('descendantsAndSelf')->get()->pluck('descendantsAndSelf.*.id')->flatten()->unique()->toArray();
                    $columns = ['job_position_id', 'department_id'];
                    $job_position_ids = [$value];
                    $department_ids = $descendant_ids;
                    $values = [$job_position_ids, $department_ids];
                    $users = $this->model::whereIn('department_id', $department_ids)
                        ->whereIn('job_position_id', $job_position_ids)
                        ->get();
                    // Định dạng dữ liệu trả về từ bảng User
                    $retrieved_data = $users->map(function ($user) {
                        return [
                            'label' => $user->account_name . ' - ' . $user->full_name,
                            'value' => $user->id,
                            'email' => $user->email
                        ];
                    })->values()->toArray();

                    
                    $additional_retrieved_data = $this->getUsersByColumnInDepartmentSub($columns, $values);
                    // Gộp dữ liệu từ bảng User và bảng DepartmentSub
                    $retrieved_data = array_merge($retrieved_data, $additional_retrieved_data);
                    break;
                case 'OPTION_SYSTEM_DEFAULT':
                    // --- Xử lý cho type 'OPTION_SYSTEM_DEFAULT' ---
                    switch ($value) {
                        case 'create_by_id':
                            // --- Xử lý cho type 'create_by_id' ---
                            if ($job_id) {
                                // Lấy thông tin job hiện tại
                                $job = SaveJob::find($job_id);
                                if ($job) {
                                    // Lấy thông tin người tạo từ job
                                    $creator = $this->model::find($job->create_by);
                                    if ($creator) {
                                        $retrieved_data = [
                                            'label' => $creator->account_name . ' - ' . $creator->full_name,
                                            'value' => $creator->id,
                                            'email' => $creator->email
                                        ];
                                    }
                                } else {
                                    // Lấy thông tin job hiện tại
                                    $process = Process::find($job_id);
                                    if ($process) {
                                        // Lấy thông tin người tạo từ job
                                        $creator = $this->model::find($process->processVersionActive->create_by);
                                        if ($creator) {
                                            $retrieved_data = [
                                                'label' => $creator->account_name . ' - ' . $creator->full_name,
                                                'value' => $creator->id,
                                                'email' => $creator->email
                                            ];
                                        }
                                    }
                                }
                            }
                            break;
                        case 'user_id':
                            if ($job_id) {
                                // Lấy thông tin job hiện tại
                                $job = SaveJob::find($job_id);
                                if ($job) {
                                    // Lấy thông tin người dùng từ job
                                    $user = $this->model::find($job->user_id);
                                    if ($user) {
                                        $retrieved_data = [
                                            'label' => $user->account_name . ' - ' . $user->full_name,
                                            'value' => $user->id,
                                            'email' => $user->email
                                        ];
                                    }
                                }
                            }
                            break;
                        case 'approve_by_id':
                            if ($job_id) {
                                // Lấy thông tin job hiện tại
                                $job = SaveJob::find($job_id);
                                if ($job) {
                                    // Lấy tất cả các jobApprovalHistories có approved_list
                                    $approvalHistories = $job->jobApprovalHistories()
                                        ->whereIn('stage_id', $stage_ids)
                                        ->whereNotNull('approved_list')
                                        ->whereNull('user_id')
                                        ->get();
                                        // dd($approvalHistories);
                                    // Mỗi history tạo thành 1 group riêng
                                    foreach ($approvalHistories as $history) {
                                        $approverIds = $history->approved_list;
                                        if ($approverIds) {
                                            // Lấy thông tin của nhóm người duyệt hiện tại
                                            $users = $this->model::whereIn('id', $approverIds)->get();
                                            $groupData = $users->map(function ($user) {
                                                return [
                                                    'label' => $user->account_name . ' - ' . $user->full_name,
                                                    'value' => $user->id,
                                                    'email' => $user->email
                                                ];
                                            })->values()->toArray();
                                            
                                            // Lưu group riêng
                                            $approve_groups[] = $groupData;
                                        }
                                    }
                                }
                            }
                            // Với approve_by_id không gán vào $retrieved_data để tránh bị merge chung
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }

            // Nếu đây không phải là trường hợp approve_by_id và có dữ liệu, thêm vào mảng $others
            if ($type !== 'approve_by_id' && !empty($retrieved_data)) {
                $others[] = $retrieved_data;
            }
        }
        // Nếu có kết quả của approve_by_id và các trường hợp khác,
        // thì đẩy những kết quả của $others vào từng group trong $approve_groups
        if (!empty($approve_groups) && !empty($others)) {
            foreach ($approve_groups as &$group) {
                foreach ($others as $other) {
                    $group[] = $other;
                }
            }
            unset($group);
            $final_data = $approve_groups;
        } else {
            $final_data = !empty($approve_groups) ? $approve_groups : $others;
        }
        // dump($final_data);
        // Nếu có dữ liệu approve_by_id thì không cần chạy qua filterFinalData
        if (!empty($approve_groups)) {
            $result = $final_data;
        } else {
            // Lọc dữ liệu cuối cùng nếu không có approve_by_id
            $result = $this->filterFinalData($final_data);
        }

        return $result;
	}

	public function filterFinalData($final_data)
    {
        $flattened = collect($final_data)->flatMap(function ($item) {
            if (empty($item)) {
                return []; // Bỏ qua mảng rỗng
            }
            // Kiểm tra xem $item có phải là mảng chứa label/value trực tiếp không
            if (isset($item['value'])) {
                return [$item]; // Trả về mảng chứa một phần tử đó
            }
            // Kiểm tra xem $item có phải là mảng chứa các mảng label/value không
            // Dấu hiệu là phần tử đầu tiên (nếu có) là một mảng và có key 'value'
            if (is_array($item) && !empty($item) && isset(array_values($item)[0]['value'])) {
                return $item; // Trả về trực tiếp mảng các phần tử đó
            }
            return []; // Bỏ qua các cấu trúc không mong muốn khác
        });
        
        
        // 3. Lọc các phần tử duy nhất dựa trên key 'value'
        $unique = $flattened->unique('value');
        
        // 4. (Tùy chọn) Lấy kết quả dưới dạng mảng PHP thông thường và reset key
        $result = $unique->values()->all();

        return $result;
    }

    public function getUsersByColumnInDepartmentSub($columns, $values): array
    {
        // Lấy ngày hiện tại
        $current_date = Carbon::now()->format('Y-m-d');

        // Khởi tạo query
        $query = DepartmentSub::query();

        // Kiểm tra nếu $columns là mảng
        if (is_array($columns)) {
            foreach ($columns as $index => $column) {
                if (isset($values[$index])) {
                    $query->whereIn($column, $values[$index]); // Thêm điều kiện where cho từng cột
                }
            }
        } else {
            // Nếu $columns không phải là mảng, xử lý như cột đơn
            $query->whereIn($columns, $values);
        }

        // Lấy tất cả user_id từ bảng DepartmentSub trong khoảng thời gian hợp lệ
        $userIds = $query->where('date_start', '<=', $current_date) // date_start phải nhỏ hơn hoặc bằng ngày hiện tại
            ->where(function ($query) use ($current_date) {
                $query->whereNull('date_end') // date_end là NULL (vô thời hạn)
                      ->orWhere('date_end', '>=', $current_date); // hoặc lớn hơn hoặc bằng ngày hiện tại
            })
            ->pluck('user_id')
            ->unique()
            ->toArray();
            
        // Lấy thông tin user từ bảng User dựa trên user_id
        $users = $this->model::whereIn('id', $userIds)->get();

        // Định dạng dữ liệu trả về
        return $users->map(function ($user) {
            return [
                'label' => $user->account_name . ' - ' . $user->full_name,
                'value' => $user->id,
                'email' => $user->email
            ];
        })->values()->toArray();
    }
}

?>