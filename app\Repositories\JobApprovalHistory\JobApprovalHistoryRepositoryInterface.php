<?php
namespace App\Repositories\JobApprovalHistory;

use App\Repositories\RepositoryInterface;

interface JobApprovalHistoryRepositoryInterface extends RepositoryInterface
{
    public function getApprovalHistoriesForJob($jobId);
    public function canUserApprove($userId, $approvalHistoryId);
    public function getPendingApprovalsForUser($userId, $dataSearch);
    public function isUserInApprovalProcess($userId, $jobId);
}
?>