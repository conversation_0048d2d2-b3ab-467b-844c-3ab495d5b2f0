<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-1">
				<CCardHeader>
					<div class="d-flex justify-content-start align-items-center">
						<b-dropdown size="lg" variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab">
							<template #button-content>
								<span class="material-icons-outlined">tune</span>
							</template>
							<b-dropdown-item 
								v-for="(tab, index) in optionTabs" 
								:key="index" 
								@click="handleClickTab(tab.value)"
							>
								{{ tab.title }} ({{ dataCounts[tab.value] }})
							</b-dropdown-item>
							<b-dropdown-divider></b-dropdown-divider>
							<b-dropdown-item>
                                <div class="d-inline-flex align-items-center">
                                    <span class="material-symbols-outlined">auto_fix_high</span>
                                    <span class="m-2">{{ $t('menu_tab.custom') }}</span>
                                </div>
                            </b-dropdown-item>
						</b-dropdown>
						<ul class="nav">
							<li v-for="(tab, index) in optionTabs" :key="index" class="nav-item">
								<a 
									:class="{ active: isActiveTab(tab.value) }" 
									class="nav-link"
									@click="handleClickTab(tab.value)"
								>
									{{ tab.title }} ({{ dataCounts[tab.value] }})
								</a>
							</li>
						</ul>
						<div class="ms-auto">
                            <router-link
                                :to="{ name: 'JobAdd' }"
                                class="btn btn-light d-flex align-items-center"
                            >
                                <span class="material-symbols-outlined me-1">add_circle</span>
								<span class="fw-normal">{{ $t('job.add') }} </span>
                            </router-link>
						</div>
					</div>
				</CCardHeader>
			</CCard>
			<CCard class="mb-4">
				<CCardHeader>
					<paginate 
						:meta="paginate.meta" 
						:links="paginate.links" 
						@page="page" 
						@per-page="perPage"
					>
					</paginate>
				</CCardHeader>
				<CCardBody>
					<job-table
						:dataJobs="dataJobs"
						@update-data-paginate="updateDataPaginate"
					>
					</job-table>
				</CCardBody>
			</CCard>
		</CCol>
	</CRow>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router';
import { useI18n } from "vue-i18n";
import JobTable from '@/views/job/JobTable.vue';
import Paginate from '@/views/paginate/Paginate.vue';
import useJobs from '@/composables/job';
import { SAVE_JOB_STATUS } from "@/constants/constants";
  
export default defineComponent({
    name: "JobListData",

	components: {
        Paginate,
		JobTable
    },
  
    setup() {
		const route = useRoute();
        const { t }  = useI18n();

		const state = reactive({
			tabActived: '' as any,
			paginate: {
				page: 1,
            	perPage: 10,
			}
		});
		
		// Sử dụng computed để optionTabs cập nhật khi ngôn ngữ thay đổi
		const optionTabs = computed(() => [
			{ value: SAVE_JOB_STATUS.ALL, title: t('option_tab_job.all') },
            { value: SAVE_JOB_STATUS.COMPLETED, title: t('option_tab_job.completed') },
			{ value: SAVE_JOB_STATUS.PENDING, title: t('option_tab_job.pending') },
			{ value: SAVE_JOB_STATUS.PROCESSING, title: t('option_tab_job.processing') },
            { value: SAVE_JOB_STATUS.CANCEL, title: t('option_tab_job.cancel') },
		]);

		const isActiveTab = (valueTabActived: string) => {
			return route.query.tab === valueTabActived;
		};

		const handleClickTab = (valueTabActived: string) => {
			state.tabActived = valueTabActived;
			getAllJobs(
				state.paginate.page, 
				state.paginate.perPage, 
				state.tabActived
			);
		};

		const { setIsLoading, paginate, dataJobs, dataCounts, getAllJobs } = useJobs();

		const refreshData = (): void => {
			state.tabActived = route.query.tab || SAVE_JOB_STATUS.ALL;
			getAllJobs(
				state.paginate.page, 
				state.paginate.perPage,
				state.tabActived
			);
        };
 
        onMounted(() => {
		 	refreshData();
		})

        const page = (page: number): void => {
            getAllJobs(
				page, 
				state.paginate.perPage,
				state.tabActived
			);
        };

        const perPage = (perPage: number): void => {
            state.paginate.perPage = perPage;
            getAllJobs(
				state.paginate.page, 
				perPage,
				state.tabActived
			);
        };

		const updateDataPaginate = (value: any): void => {
			getAllJobs(
				value.currentPage, 
				value.perPage,
				state.tabActived
			);
			setIsLoading.value = false;
        }

		return {
			state,
			optionTabs,
			setIsLoading,
			paginate,
			isActiveTab,
			handleClickTab,
			dataJobs,
			dataCounts,
			refreshData,
            page,
            perPage,
			updateDataPaginate,
		}
    }
});
</script>

<style scoped>

</style>
<style src="@vueform/multiselect/themes/default.css"></style>