<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Role extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'roles';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'is_hidden',
        'tenant_id',
        'create_by',
        'update_by',
        'expired_at',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'expired_at' => 'date',
    ];

    /**
     * Quan hệ nhiều-nhiều với users
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'role_user')
            ->using(\App\Models\RoleUser::class)
            ->withTimestamps();
    }
    /**
     * Quan hệ nhiều-nhiều với permissions
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'permission_role')
            ->using(PermissionRole::class) // Use PermissionRole as the pivot model
            ->withPivot('scope') // We only need 'scope' here now, 'id' is handled by PermissionRole
            ->withTimestamps();
    }
    public function createBy()
    {
        return $this->belongsTo(User::class, 'create_by');
    }
    public function updateBy()
    {
        return $this->belongsTo(User::class, 'update_by');
    }

    /**
     * Kiểm tra vai trò có quyền cụ thể không
     *
     * @param string $permission Tên quyền cần kiểm tra
     * @return bool
     */
    public function hasPermission($permission)
    {
        return $this->permissions()->where('slug', $permission)->exists();
    }
}
