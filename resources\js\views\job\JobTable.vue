<template>
    <CTable align="middle" responsive>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th class="align-middle table__td--width">
                        {{ $t('job.name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.account_name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.full_name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.status') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.department') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.job_position') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.name_workflow') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.type_workflow') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.created_by') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('job.created_at') }}
                    </th>
                </tr>
            </thead>
            <tbody v-if="checkDataNotEmpty">
                <tr v-for="(job, index) in dataJobs" :key="index">
                    <td class="align-middle">
                        <router-link :to="{ name: 'JobDetail', params: { id: job.id } }" class="text-decoration-none">
                            {{ job?.name || $t('common.no_data') }}
                        </router-link>
                    </td>
                    <td class="align-middle">
                        {{ job?.assigned_user?.account_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ job?.assigned_user?.full_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        <span class="badge rounded-pill bg-info" v-if="job.status === SAVE_JOB_STATUS.PENDING">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_job.pending') }}
                            </small>
                        </span>
                        <span class="badge rounded-pill bg-warning" v-else-if="job.status === SAVE_JOB_STATUS.PROCESSING">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_job.processing') }}
                            </small>
                        </span>
                        <span class="badge rounded-pill bg-success" v-else-if="job.status === SAVE_JOB_STATUS.COMPLETED">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_job.completed') }}
                            </small>
                        </span>
                        <span class="badge rounded-pill bg-danger" v-else-if="job.status === SAVE_JOB_STATUS.CANCEL">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_job.cancel') }}
                            </small>
                        </span>
                    </td>
                    <td class="align-middle">
                        {{ job?.department?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ job?.job_position?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ job?.process_version?.process?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ job?.process_version?.process?.process_group?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ job?.created_by?.full_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ job?.created_at ? formatDate(job?.created_at) : $t('common.no_data') }}
                    </td>
                </tr>
            </tbody>
            <tbody v-else>
                <tr>
                    <td colspan="10" class="align-middle text-center">
                        {{ $t('search.no_matching_records_found') }}
                    </td>
                </tr>
            </tbody>
        </table>
    </CTable>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { SAVE_JOB_STATUS } from "@/constants/constants";
import moment from 'moment';

export default defineComponent({
    name: 'JobTable',
    emits: ['update-data-paginate'],

    props: {
        dataJobs: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const checkDataNotEmpty = computed<boolean>(() => {
            return props.dataJobs.length > 0;
        });

        const formatDate = (date: string) => {
            return moment(date).format('DD/MM/YYYY');
        };

        return {
            checkDataNotEmpty,
            SAVE_JOB_STATUS,
            formatDate,
        }
    },
});
</script>
<style type="text/css" scoped>
</style>