<?php

// Khai báo namespace tương ứng với vị trí file
namespace App\Enums;

use Illuminate\Support\Str;

// Định nghĩa Enum với kiểu giá trị nền là int
enum RoleIsHidden: int
{
    // Định nghĩa các trường hợp (cases) có thể có và giá trị số nguyên tương ứng
    case SHOW = 1;      // Hiển thị
    case HIDDEN = 0;   // Ẩn

    /**
     * L<PERSON>y nhãn (label) đã được dịch cho từng trường hợp của Enum.
     *
     * @return string
     */
    public function label(): string
    {
        // 1. Tạo phần giữa của key từ tên lớp Enum (e.g., PermissionStatus -> permission_status)
        $enum_key = Str::snake(class_basename(static::class));

        // 2. L<PERSON>y phần cuối của key từ tên của case (e.g., ALL -> all)
        $case_key = strtolower($this->name);

        // 3. <PERSON>h<PERSON><PERSON> lại thành key dịch đ<PERSON>y đủ (e.g., "enums.permission_status.all")
        $translation_key = "enums.{$enum_key}.{$case_key}";

        // 4. Trả về bản dịch bằng hàm helper __()
        return __($translation_key);
    }

    /**
     * Tạo một instance Enum từ một giá trị chuỗi (string).
     *
     * @param string $status Chuỗi cần chuyển đổi ('all', 'active', 'unactive').
     * @return self
     */
    public static function fromString(string $status): self
    {
        return match (strtolower($status)) {
            'show' => self::SHOW,
            'hidden' => self::HIDDEN,
        };
    }
}