<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('account_name', 50)->unique(); // Tài khoản
            $table->string('full_name', 100); // Họ và tên
            $table->string('email')->unique(); // Địa chỉ email
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password'); // Mật khẩu
            $table->string('two_factor_code', 6)->nullable();
            $table->dateTime('two_factor_expires_at')->nullable();
            $table->uuid('department_id')->nullable();  // ID phòng ban
            $table->uuid('rank_id')->nullable();  // ID chức vụ
            $table->uuid('job_position_id')->nullable();  // ID vị trí công việc
            $table->uuid('cost_center_id')->nullable();  // ID cost-center
            $table->boolean('is_active')->default(true)->comment('0 - Không hoạt động, 1 - Hoạt động');  // Trạng thái hoạt động
            $table->boolean('not_activated')->default(false)->comment('0 - Chưa kích hoạt, 1 - Đã kích hoạt');  // Trạng thái kích hoạt
            $table->dateTime('activation_date')->nullable(); // Ngày kích hoạt
            $table->string('language', 5)->nullable(); // Ngôn ngữ
            $table->uuid('color_id')->nullable(); // ID mã màu
            $table->uuid('tenant_id')->nullable();  // Thuộc đơn vị
            $table->uuid('create_by')->nullable();  // Người tạo
            $table->rememberToken();
            $table->timestamp('roles_updated_at')->nullable();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });

        // Tạo chỉ mục
        Schema::table('users', function (Blueprint $table) {
            $table->index(['id', 'department_id', 'rank_id', 'job_position_id', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
