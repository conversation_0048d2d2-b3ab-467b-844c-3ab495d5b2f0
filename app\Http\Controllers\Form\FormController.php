<?php

namespace App\Http\Controllers\Form;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Field\FieldRepositoryInterface;

class FormController extends Controller
{
    private $fieldRepository;

    public function __construct(
        FieldRepositoryInterface $fieldRepository
    ) 
    {
        $this->fieldRepository = $fieldRepository;
    }

    public function getFieldsByFormId(Request $request)
    {
        $form_id = $request->formId;
        $stage_id = $request->stageId;
        $fields  = $this->fieldRepository->fieldsByFormId($form_id, $stage_id);

        return response()->json($fields);
    }
}
