<?php

namespace App\Services;

use App\Repositories\JobFieldValue\JobFieldValueRepositoryInterface;
use App\Repositories\JobFieldValueObject\JobFieldValueObjectRepositoryInterface;
use App\Repositories\ModelSystem\ModelSystemRepositoryInterface;
use Illuminate\Support\Collection;

class FormDataProcessingService
{
    protected $jobFieldValueRepository;
    protected $jobFieldValueObjectRepository;
    protected $modelSystemRepository;
    protected $dataProcessingService;
    protected $fileUploadService;

    public function __construct(
        JobFieldValueRepositoryInterface $jobFieldValueRepository,
        JobFieldValueObjectRepositoryInterface $jobFieldValueObjectRepository,
        ModelSystemRepositoryInterface $modelSystemRepository,
        DataProcessingService $dataProcessingService,
        FileUploadService $fileUploadService
    ) {
        $this->jobFieldValueRepository = $jobFieldValueRepository;
        $this->jobFieldValueObjectRepository = $jobFieldValueObjectRepository;
        $this->modelSystemRepository = $modelSystemRepository;
        $this->dataProcessingService = $dataProcessingService;
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Xử lý dữ liệu form
     */
    public function processFormData($form_data, Collection $fields, $job_id, $key_file_uploads = null)
    {
        if (!isset($form_data)) {
            return false;
        }

        $form_data = is_string($form_data) ? json_decode($form_data, true) : $form_data;
        
        // Xử lý key_file_uploads, trường dữ liệu các file trên form
        if (isset($key_file_uploads)) {
            foreach ($key_file_uploads as $file_upload_key => $file_upload_values) {
                // Kiểm tra xem key có tồn tại trong form_data không
                if (isset($form_data[$file_upload_key])) {
                    // Thay thế giá trị tại $form_data[$file_upload_key] bằng file_upload_values
                    $form_data[$file_upload_key] = $file_upload_values;
                }
            }
        }
        
        $table_users = 'users'; // Bảng người dùng
        $table_departments = 'departments'; // Bảng phòng ban
        
        foreach ($form_data as $form_key => $form_value) {
            $field = $fields->where('keyword', $form_key)->first();
            if (!$field) {
                continue;
            }
            
            $field_id = $field->id;
            $field_type = $field->type;
            
            if (is_array($form_value)) {
                if (in_array($field_type, ['SELECT', 'CHECKLIST'])) {
                    $data_job_field_value = [
                        'job_id' => $job_id,
                        'field_id' => $field_id,
                        'field_value' => $form_value,
                    ];
                    $this->jobFieldValueRepository->create($data_job_field_value);
                } else if (in_array($field_type, ['OBJECTSYSTEM', 'USER', 'DEPARTMENT'])) {
                    // Lấy tất cả các giá trị của key label trong $form_value
                    $labels = [];
                    $value_ids = [];
                    if (isset($form_value['value'])) {
                        // If $form_value has a single 'label', add it to $labels array
                        $labels[] = $form_value['label'];
                        $value_ids[] = $form_value['value'];
                    } else {
                        // If $form_value is an array of items, extract 'label' from each item
                        $labels = array_column($form_value, 'label');
                        $value_ids = array_column($form_value, 'value');
                    }
                    $data_job_field_value = [
                        'job_id' => $job_id,
                        'field_id' => $field_id,
                        'field_value' => $value_ids,
                    ];
                    $job_field_value = $this->jobFieldValueRepository->create($data_job_field_value);
                    $job_field_value_id = $job_field_value->id;
                    
                    if ($field_type == 'OBJECTSYSTEM') {
                        $table = $field->object_table;
                    } else if ($field_type == 'USER') {
                        $table = $table_users;
                    } else if ($field_type == 'DEPARTMENT') {
                        $table = $table_departments;
                    }
                    
                    $models = $this->modelSystemRepository->getModelSystem();
                    $object_type = $models[$table] ?? null;
                    
                    if (isset($object_type)) {
                        // Xử lý việc lưu các đối tượng OBJECTSYSTEM, USER, DEPARTMENT
                        $object_id = $form_value['value'] ?? null;
                        if (isset($object_id)) {
                            $data_job_field_value_object = [
                                'job_field_value_id' => $job_field_value_id,
                                'object_id' => $object_id,
                                'object_type' => $object_type,
                            ];
                            $this->jobFieldValueObjectRepository->create($data_job_field_value_object);
                        } else {
                            foreach ($form_value as $item) {
                                $data_job_field_value_object = [
                                    'job_field_value_id' => $job_field_value_id,
                                    'object_id' => $item['value'],
                                    'object_type' => $object_type,
                                ];
                                $this->jobFieldValueObjectRepository->create($data_job_field_value_object);
                            }
                        }
                    }
                } else if (in_array($field_type, ['FILEUPLOAD'])) {
                    $file_upload_name_news = $this->fileUploadService->uploadFiles($form_value, 'file_uploads');
                    $data_job_field_value = [
                        'job_id' => $job_id,
                        'field_id' => $field_id,
                        'field_value' => $file_upload_name_news,
                    ];
                    $this->jobFieldValueRepository->create($data_job_field_value);
                }
            } else {
                // Kiểm tra những value nào trong $form_value có dấu phẩy sẽ xử lý
                if (is_string($form_value) && strpos($form_value, ',') !== false) {
                    // Xử lý giá trị có dấu phẩy
                    $form_value = str_replace(',', '', $form_value);
                }
                $data_job_field_value = [
                    'job_id' => $job_id,
                    'field_id' => $field_id,
                    'field_value' => isset($form_value) ? $form_value : NULL,
                ];
                $this->jobFieldValueRepository->create($data_job_field_value);
            }
        }
        
        return true;
    }

    /**
     * Xử lý dữ liệu bảng
     */
    public function processTableData($key_tables, Collection $fields, $job_id, $key_file_upload_childrens = null)
    {
        if (!isset($key_tables)) {
            return false;
        }
        
        $table_users = 'users'; // Bảng người dùng
        $table_departments = 'departments'; // Bảng phòng ban
        
        foreach ($key_tables as $table_key => $table_values) {
            $field = $fields->where('keyword', $table_key)->first();
            if (!$field) {
                continue;
            }
            
            $field_id = $field->id;
            $data_job_field_value = [
                'job_id' => $job_id,
                'field_id' => $field_id,
            ];
            $this->jobFieldValueRepository->create($data_job_field_value);
            $table_values = is_string($table_values) ? json_decode($table_values, true) : $table_values;
            
            // Mảng để lưu trữ các giá trị của trường
            $field_values = [];
            // Lặp qua từng dòng trong $table_values, gặp các giá trị của các dòng lại thành 1 mảng
            foreach ($table_values as $table_item_values) {
                // Lặp qua từng cột trong dòng
                foreach ($table_item_values as $item_key => $item_values) {
                    $field_values[$item_key][] = $item_values;
                }
            }
            
            // Xử lý key_file_upload_childrens
            if (isset($key_file_upload_childrens)) {
                foreach ($key_file_upload_childrens as $child_key => $child_value) {
                    foreach ($child_value as $key => $value) {
                        // Kiểm tra xem key có tồn tại trong field_values không
                        if (isset($field_values[$key])) {
                            // Kiểm tra xem index có tồn tại trong field_values[key] không
                            if (isset($field_values[$key][$child_key])) {
                                // Thay thế giá trị tại field_values[key][child_key] bằng key_file_upload_childrens[key][index]
                                $field_values[$key][$child_key] = $value;
                            }
                        }
                    }
                }
            }
            
            // Tạo bản ghi của 1 trường đó và chứa tất cả value của trường đó ở các dòng
            foreach ($field_values as $field_key => $values) {
                // Tìm kiếm trong $fields
                $field = $fields->where('keyword', $field_key)->first();
                if (!$field) {
                    continue;
                }
                
                $field_id = $field->id;
                $field_type = $field->type;
                
                // Kiểm tra nếu $field_type là OBJECTSYSTEM, USER, DEPARTMENT
                if (in_array($field_type, ['OBJECTSYSTEM', 'USER', 'DEPARTMENT'])) {
                    $labels = $this->dataProcessingService->extractLabelsGrouped($values);
                    $value_ids = $this->dataProcessingService->extractValueIdsGrouped($values);
                    $data_job_field_value = [
                        'job_id' => $job_id,
                        'field_id' => $field_id,
                        'field_value' => $value_ids,
                    ];
                    $job_field_value = $this->jobFieldValueRepository->create($data_job_field_value);
                    $job_field_value_id = $job_field_value->id;
                    
                    // Xử lý việc lưu các đối tượng OBJECTSYSTEM, USER, DEPARTMENT
                    if ($field_type == 'OBJECTSYSTEM') {
                        $table = $field->object_table;
                    } else if ($field_type == 'USER') {
                        $table = $table_users;
                    } else if ($field_type == 'DEPARTMENT') {
                        $table = $table_departments;
                    }
                    
                    $models = $this->modelSystemRepository->getModelSystem();
                    $object_type = $models[$table] ?? null;
                    $object_ids = $this->dataProcessingService->extractAllValueIdsByObject($values);
                    
                    if (isset($object_type)) {
                        foreach ($object_ids as $object_id) {
                            $data_job_field_value_object = [
                                'job_field_value_id' => $job_field_value_id,
                                'object_id' => $object_id,
                                'object_type' => $object_type,
                            ];
                            $this->jobFieldValueObjectRepository->create($data_job_field_value_object);
                        }
                    }
                } else if (in_array($field_type, ['FILEUPLOAD'])) {
                    $file_name_news_grouped = [];
                    foreach ($values as $files) {
                        $file_name_news = [];
                        if (isset($files)) {
                            $file_name_news = $this->fileUploadService->uploadFiles($files, 'file_uploads');
                        }
                        $file_name_news_grouped[] = $file_name_news;
                    }
                    $data_job_field_value = [
                        'job_id' => $job_id,
                        'field_id' => $field_id,
                        'field_value' => $file_name_news_grouped,
                    ];
                    $this->jobFieldValueRepository->create($data_job_field_value);
                } else {
                    $values = array_filter($values, function($value) {
                        return !is_null($value);
                    });
                    
                    // Kiểm tra những item nào trong $values có dấu phẩy sẽ xử lý dùng array_map
                    $values = array_map(function($value) {
                        if (is_string($value) && strpos($value, ',') !== false) {
                            // Xử lý giá trị có dấu phẩy
                            return str_replace(',', '', $value);
                        }
                        return $value;
                    }, $values);

                    $data_job_field_value = [
                        'job_id' => $job_id,
                        'field_id' => $field_id,
                        'field_value' => $values,
                    ];
                    $this->jobFieldValueRepository->create($data_job_field_value);
                }
            }
        }
        
        return true;
    }
} 