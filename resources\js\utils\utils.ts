const numberCommas = (value: any) => {
    if (value === null || value === undefined) return '';

    return value.toLocaleString("en-US");
}

const checkLocale = (language: any) => {
    const scopeLanguage: string[] = ['vi', 'en'];
    let locale: string | any = 'vi';
    
    if (language) {
        if (scopeLanguage.includes(language)) {
            locale = language;
        }
    }

    return locale;
}

const checkInteger = (value: number) => {
    const numberValue = Number(value);
    return Number.isInteger(numberValue) && numberValue >= 0;
}

const generateStandardSlug = (str: string) => {
    return str
    .normalize('NFD')             // Tách ký tự có dấu thành ký tự gốc + dấu
    .replace(/[\u0300-\u036f]/g, '') // Loại bỏ dấu thanh (diacritics)
    .toLowerCase()                // Chuyển thành chữ thường
    .replace(/đ/g, 'd')           // Chuyển đổi 'đ' thành 'd'
    .replace(/\s+/g, '_')         // << THAY THẾ khoảng trắng bằng '_'
    .replace(/[^a-z0-9_]/g, '')   // << LOẠI BỎ ký tự không phải a-z, 0-9, hoặc '_'
    .replace(/_+/g, '_')          // << THAY THẾ nhiều '_' liên tiếp bằng một '_'
    .replace(/^_+|_+$/g, '');     // << XÓA '_' ở đầu và cuối chuỗi
}


export {
    numberCommas,
    checkLocale,
    checkInteger,
    generateStandardSlug
}