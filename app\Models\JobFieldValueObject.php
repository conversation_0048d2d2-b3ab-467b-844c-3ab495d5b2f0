<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class JobFieldValueObject extends Model
{
    use SoftDeletes, HasUuids;
    protected $table = 'job_field_value_objects';
    protected $primaryKey = 'id';

    protected $fillable = [
        'job_field_value_id',
        'object_id',
        'object_type',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];
    
    /**
     * Thuộc tính phụ để lưu trữ đối tượng liên kết
     */
    protected $relatedObject = null;
    
    /**
     * Thuộc tính có thể được thêm vào khi JSON serialization
     */
    // protected $appends = ['related_object'];
    
    /**
     * Lấy đối tượng liên kết dựa trên object_type và object_id
     * Quan hệ polymorphic cho phép liên kết với nhiều model khác nhau
     */
    public function objectRelation()
    {
        return $this->morphTo(null, 'object_type', 'object_id');
    }
    
    /**
     * Lấy giá trị trường công việc liên kết
     */
    public function jobFieldValue()
    {
        return $this->belongsTo(JobFieldValue::class, 'job_field_value_id', 'id');
    }
    
    /**
     * Setter cho relatedObject
     */
    public function setRelatedObjectAttribute($value)
    {
        $this->relatedObject = $value;
    }
    
    /**
     * Getter cho relatedObject
     */
    public function getRelatedObjectAttribute()
    {
        if ($this->relatedObject !== null) {
            return $this->relatedObject;
        }
        
        // Nếu chưa có relatedObject, thử lấy từ quan hệ objectRelation
        return $this->objectRelation;
    }
}
