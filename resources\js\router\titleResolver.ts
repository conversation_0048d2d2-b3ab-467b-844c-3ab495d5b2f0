import i18n from '@/lang/i18n';

/**
 * <PERSON><PERSON>y tiêu đề route dựa trên khóa i18n
 * @param titleKey Khóa i18n để lấy tiêu đề
 * @returns Tiêu đề đã được dịch
 */
export function resolveRouteTitle(titleKey: string): string {
	if (!titleKey) return '';

	try {
		// Type assertion with proper checking
		const translator = i18n.global.t as (key: string) => string;
		return translator(titleKey);
	} catch (error) {
		console.warn('Error translating title key:', titleKey, error);
		return titleKey; // Trả về key gốc nếu không thể dịch
	}
}
