<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\PermissionGroup;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::create([
            'account_name' => 'super_admin',
            'password' => Hash::make('Tvnsoft@2024'),
            'full_name' => 'Super Admin',
            'email' => '<EMAIL>',
            'language' => 'vi',
        ]);

        $permissionGroup = PermissionGroup::create([
            'name' => 'Thiết lập',
            'is_active' => 1,
            'create_by' => $user->id,
        ]);

        Permission::create([
            'name' => 'Thiết lập hệ thống',
            'slug' => 'SUPER-ADMIN-SETTING',
            'permission_group_id' => $permissionGroup->id,
            'is_hidden' => 0,
            'is_active' => 1,
            'create_by' => $user->id,
        ]);

        $role = Role::create([
            'name' => 'Quản trị viên cấp cao',
            'is_hidden' => 0,
            'create_by' => $user->id,
        ]);

        

        $user->assignRoles($role);
    }
}
