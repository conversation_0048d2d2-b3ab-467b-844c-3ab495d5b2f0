<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Permission::create([
            'name' => 'Thiết lập hệ thống',
            'slug' => 'SUPER-ADMIN-SETTING',
            'is_hidden' => 0,
            'is_active' => 1,
        ]);

        $user = User::create([
            'account_name' => 'super_admin',
            'password' => Hash::make('Tvnsoft@2024'),
            'full_name' => 'Super Admin',
            'email' => '<EMAIL>',
            'language' => 'vi',
        ]);

        $role = Role::create(['name' => 'Super Admin']);

        

        $user->assignRole($role);
    }
}
