import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";
import { Meta, Link } from "@/types/index";

export default function useRoles() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);
    const paginate = reactive({
        meta: {
            from: '',
            to: '',
            total: '',
            lastPage: '',
            currentPage: '',
            perPage: '',
        } as Meta,
        links: {
            prev: '',
            next: '',
        } as Link,
    });

    const dataRoles = ref<any>([]);
    const dataRoleOptions = ref<any>([]);
    const dataCounts = ref<any>([]);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const storeRole = async (formRoleData: any) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/roles`, formRoleData);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storeRole(formRoleData);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    const getAllRoles = async (
        page: number, 
        perPage: number,
        valueTabActived: string,
    ) => {
        setIsLoading.value = true;
        try {
            const tab = valueTabActived !== '' ? { tab: valueTabActived } : null;
            let response = await axios.get('/api/roles', {
                params: {
                    tab: tab !== null ? valueTabActived : tab,
                    page: page,
                    perPage: perPage,
                }
            });
            
            if (response.data.status == 'success') {
                dataCounts.value          = response.data.counts;
                dataRoles.value           = response.data.roles.data;
                paginate.links.prev       = response.data.roles.links.prev;
                paginate.links.next       = response.data.roles.links.next; 
                paginate.meta.currentPage = response.data.roles.current_page;
                paginate.meta.perPage     = response.data.roles.per_page;
                paginate.meta.from        = response.data.roles.from;
                paginate.meta.to          = response.data.roles.to;
                paginate.meta.total       = response.data.roles.total;
                paginate.meta.lastPage    = response.data.roles.last_page;
            }

            const query = {
                ...tab
            };

            await router.push({ name: 'RoleListData', query: { ...query, page, perPage,  } }).catch(()=>{});
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getAllRoles(page, perPage, valueTabActived);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        } 
    }

    const getAllRoleOptions = async () => {
        setIsLoading.value = true;
        try {
            let response = await axios.get('/api/roles/options');

            dataRoleOptions.value = response?.data;

        } catch (error: any) {
         if (!error.response) {
             setTimeout(() => {
                 getAllRoleOptions();
             }, 1000);

             return;
         }

        catchError(error);  
     } finally {
            setIsLoading.value = false;
        } 
    }

    return {
        setIsLoading,
        paginate,
        dataRoles,
        getAllRoles,
        getAllRoleOptions,
        storeRole,
        dataCounts,
        dataRoleOptions,
    }
}