<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('process_groups', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('name', 200);  // Tên quy trình
            $table->boolean('is_active')->default(true)->comment('0 - Không hoạt động, 1 - Hoạt động');  // Trạng thái hoạt động
            $table->uuid('tenant_id');  // Thuộc đơn vị
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('process_groups', function (Blueprint $table) {
            $table->index(['id', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('process_groups');
    }
};
