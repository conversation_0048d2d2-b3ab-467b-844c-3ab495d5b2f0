<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class StageTransition extends Model
{
    use HasUuids;
    
    protected $table = 'stage_transitions';
    protected $primaryKey = 'id';

    protected $fillable = [
        'from_stage_id',
        'to_stage_id',
        'back_to_stage_id',
        'action_id',
        'process_version_id',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Tạo cho tôi quan hệ với nhiều với bảng StageTransitionCondition
    public function stageTransitionConditions()
    {
        return $this->hasMany('App\Models\StageTransitionCondition', 'stage_transition_id', 'id');
    }

    /**
     * Quan hệ với Stage từ from_stage_id
     */
    public function fromStage()
    {
        return $this->belongsTo('App\Models\Stage', 'from_stage_id', 'id');
    }

    /**
     * Quan hệ với Stage từ to_stage_id
     */
    public function toStage()
    {
        return $this->belongsTo('App\Models\Stage', 'to_stage_id', 'id');
    }

    /**
     * Quan hệ với Stage từ back_to_stage_id
     */
    public function backToStage()
    {
        return $this->belongsTo('App\Models\Stage', 'back_to_stage_id', 'id');
    }

    /**
     * Quan hệ với Action từ action_id
     */
    public function action()
    {
        return $this->belongsTo('App\Models\Action', 'action_id', 'id');
    }
}
