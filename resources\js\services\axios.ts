import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import router from '@/router';
import Cookies from 'js-cookie';

// Use hardcoded baseURL to avoid TypeScript errors
// In a real production environment, you would use environment variables
const baseURL = '/api';

// Create axios instance with baseURL
const api = axios.create({
	baseURL,
	withCredentials: true, // Include cookies in requests
	headers: {
		'Content-Type': 'application/json',
		'Accept': 'application/json',
		'X-Requested-With': 'XMLHttpRequest'
	}
});
// Set token from cookies on startup
const token = Cookies.get('__Host_tvnas_token');
if (token) {
	api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
}

// Request interceptor
api.interceptors.request.use(
	config => {
		const token = Cookies.get('__Host_tvnas_token');
		
		// If token exists, add to Authorization header
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		
		return config;
	},
	error => {
		return Promise.reject(error);
	}
);

// Response interceptor
api.interceptors.response.use(
	response => {
		return response;
	},
	async error => {
		const originalRequest = error.config;
		const authStore = useAuthStore();
		
		// Check if error is due to expired token (401 Unauthorized)
		if (error.response && error.response.status === 401 && !originalRequest._retry) {
			originalRequest._retry = true;
			
			try {
				// Try to refresh the token
				const refreshed = await authStore.refreshToken();
				
				if (refreshed) {
					// Retry the original request with new token
					return api(originalRequest);
				} else {
					// If refresh fails, redirect to login
					authStore.clearAuthData();
					// Save the current path for redirect after login
					Cookies.set('redirect_path', router.currentRoute.value.fullPath, { expires: 1, sameSite: 'strict' });
					router.push('/login');
				}
			} catch (refreshError) {
				console.error('Error refreshing token:', refreshError);
				authStore.clearAuthData();
				// Save current path before redirecting
				Cookies.set('redirect_path', router.currentRoute.value.fullPath, { expires: 1, sameSite: 'strict' });
				router.push('/login');
			}
		}
		
		// Handle permission denied (403 Forbidden)
		if (error.response && error.response.status === 403) {
			// Check if permissions were updated by admin
			await authStore.checkPermissionsUpdate();
			
			// Still has 403 after permission check, redirect to unauthorized page
			router.push('/home');
		}
		
		return Promise.reject(error);
	}
);

export default api;
