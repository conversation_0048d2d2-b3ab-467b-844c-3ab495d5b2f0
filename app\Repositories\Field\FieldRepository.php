<?php
namespace App\Repositories\Field;

use App\Repositories\EloquentRepository;
use App\Enums\FieldStatus;

class FieldRepository extends EloquentRepository implements FieldRepositoryInterface
{
	public function getModel()
	{
		return \App\Models\Field::class;
	}
	public function fieldsByFormId($form_id, $stage_id)
	{
		$select_columns = [
			'id',
			'keyword',
			'display_name',
			'display_name_en',
			'type',
			'default_value',
			'required',
			'order',
			'min_equal',
			'max_equal',
			'stage_id',
			'placeholder',
			'placeholder_en',
			'column_width',
			'form_id',
			'not_edit',
			'multiple',
			'options',
			'object_table',
			'column_table',
			'sub_column_table',
			'parent_id',
			'is_active',
		];

		$fields = $this->model
			->with([
				'children' => function ($query) use ($select_columns, $stage_id) {
					$query->select($select_columns)->where('stage_id', $stage_id); 
				}
			])
			->where('form_id', $form_id)
			->select($select_columns)
			->where('stage_id', $stage_id)
			->where('is_active', FieldStatus::TRUE->value)
			->get();
			
		return $fields;	
	}

	public function fieldsByFormIdAll($form_id)
	{
		$fields = $this->model
			->with(['children'])
			->where('form_id', $form_id)
			->where('is_active', FieldStatus::TRUE->value)
			->get();
			
		return $fields;	
	}
}

?>