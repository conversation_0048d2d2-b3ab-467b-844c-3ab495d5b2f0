<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_field_value_objects', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('job_field_value_id');  // ID giá trị trường
            $table->uuid('object_id');  // ID của đối tượng
            $table->string('object_type', 200);  // Lo<PERSON>i đối tượng (tên class)
            $table->timestamps();
            $table->softDeletes();
        });

        // Tạo chỉ mục
        Schema::table('job_field_value_objects', function (Blueprint $table) {
            $table->index(['id', 'job_field_value_id', 'object_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_field_value_objects');
    }
};
