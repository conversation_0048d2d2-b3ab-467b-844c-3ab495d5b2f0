<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_field_values', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('job_id');  // Thuộc công việc
            $table->uuid('field_id');  // Thuộc trường
            $table->json('field_value')->nullable();  // Giá trị
            $table->timestamps();
            $table->softDeletes();
        });

        // Tạo chỉ mục
        Schema::table('job_field_values', function (Blueprint $table) {
            $table->index(['id', 'job_id', 'field_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_field_values');
    }
};
