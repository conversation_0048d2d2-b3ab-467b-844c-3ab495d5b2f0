// src/composables/useContextMenu.js

import { ref, onMounted, onBeforeUnmount } from 'vue';

export function useContextMenu() {
    const isContextMenuVisible = ref(false);
	const contextMenuPosition = ref({ x: 0, y: 0 });
	const selectedField: any = ref(null);  // Lưu trữ thông tin của trường đã chọn
	const indexField: any = ref(null);  // Lưu trữ thông tin của trường được chọn
  	const pressTimer : any = ref(null);  // Sử dụng để theo dõi thời gian nhấn lâu
  	// Hiển thị context menu
	const showContextMenu = (event: any, field: any, index: number) => {
		event.preventDefault(); // Ngừng hiển thị menu chuột phải mặc định
		contextMenuPosition.value = { x: event.pageX, y: event.pageY };
		isContextMenuVisible.value = true;
		selectedField.value = field; // Lư<PERSON> lại thông tin field được chọn
		indexField.value = index;
	};

	// Long press - Bắt đầu nhấn lâu
	const startPress = (event: any, field: any, index: number) => {
		pressTimer.value = setTimeout(() => {
		showContextMenu(event, field, index); // Giả lập hành động chuột phải trên mobile
		}, 500); // Thời gian nhấn lâu (500ms)
	};
	
	// Long press - Kết thúc nhấn lâu
	const endPress = () => {
		clearTimeout(pressTimer.value); // Xóa thời gian nhấn lâu nếu người dùng thả ngón tay quá sớm
	};

	// Ẩn context menu khi click ngoài
	const hideContextMenu = () => {
		isContextMenuVisible.value = false;
	};

	// Lắng nghe sự kiện để ẩn menu khi click ngoài
	onMounted(() => {
		window.addEventListener('click', hideContextMenu);
	});

	onBeforeUnmount(() => {
		window.removeEventListener('click', hideContextMenu);
	});

	return {
		isContextMenuVisible,
		contextMenuPosition,
		selectedField,
		indexField,
		showContextMenu,
		hideContextMenu,
		startPress,
		endPress,
	};
}
