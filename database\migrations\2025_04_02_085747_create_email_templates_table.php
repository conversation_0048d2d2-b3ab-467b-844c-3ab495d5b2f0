<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('name', 100);  // Tiêu đề
            $table->string('slug', 100);  // Slug tiêu đề
            $table->string('name_title', 255);  // Tiêu đề email
            $table->text('content');  // Nội dung email
            $table->string('from_email', 100);  // Gửi đi từ email nào
            $table->json('to_emails');  // Email TO
            $table->json('cc_emails')->nullable();  // Email CC
            $table->json('bcc_emails')->nullable();  // Email BCC
            $table->json('files')->nullable();  // <PERSON><PERSON><PERSON> kèm file
            $table->uuid('process_version_id');  // Thuộc phiên bản quy trình
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('email_templates', function (Blueprint $table) {
            $table->index(['id', 'process_version_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};
