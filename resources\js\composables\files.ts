import Cookies from 'js-cookie';

export default function useFiles() {

    const getFileName = (filePath: string): string => {
        return filePath.split('/').pop() || filePath;
    };

    const getFileUrl = (filePath: string): string => {
        const filename = getFileName(filePath);
        const token = Cookies.get('__Host_tvnas_token');
        return `/api/files/${encodeURIComponent(filename)}${token ? `?token=${encodeURIComponent(token)}` : ''}`;
    };

    const getDownloadUrl = (filePath: string): string => {
        const filename = getFileName(filePath);
        const token = Cookies.get('__Host_tvnas_token');
        return `/api/files/download/${encodeURIComponent(filename)}${token ? `?token=${encodeURIComponent(token)}` : ''}`;
    };

    const isImageFile = (filePath: string): boolean => {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
        return imageExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
    };

    const isPdfFile = (filePath: string): boolean => {
        return filePath.toLowerCase().endsWith('.pdf');
    };

    const isPreviewableFile = (filePath: string): boolean => {
        return isImageFile(filePath) || isPdfFile(filePath);
    };

    const downloadFile = async (filePath: string): Promise<void> => {
        try {
            const response = await fetch(getDownloadUrl(filePath));

            if (!response.ok) {
                throw new Error('Download failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = getFileName(filePath);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading file:', error);
            throw error;
        }
    };

    return {
        getFileName,
        getFileUrl,
        getDownloadUrl,
        isImageFile,
        isPdfFile,
        isPreviewableFile,
        downloadFile
    }
}