import { defineComponent, h, onMounted, ref, resolveComponent, computed } from 'vue';
import { RouterLink, useRoute } from 'vue-router';

import { CBadge, CSidebarNav, CNavItem, CNavGroup, CNavTitle } from '@coreui/vue';
import { getLocalizedMenuItems } from '@/_nav';

import simplebar from 'simplebar-vue';
import 'simplebar-vue/dist/simplebar.min.css';

import { useAuthStore } from '@/stores/auth';

const normalizePath = (path: any) =>
    decodeURI(path)
        .replace(/#.*$/, '')
        .replace(/(index)?\.(html)$/, '');

const isActiveLink = (route: any, link: any) => {
    if (!link) {
        return false;
    }
    return normalizePath(route.path) === normalizePath(link);
};

const isActiveItem = (route: any, item: any) => {
    if (isActiveLink(route, item.to)) {
        return true;
    }
    return item.items ? item.items.some((child: any) => isActiveItem(route, child)) : false;
};

const AppSidebarNav = defineComponent({
    name: 'AppSidebarNav',
    components: {
        CNavItem,
        CNavGroup,
        CNavTitle,
    },
    setup() {
        const route = useRoute();
        const firstRender = ref(true);

        onMounted(() => {
            firstRender.value = false;
        });

        const authStore = useAuthStore();
        
        // Sử dụng computed property để cập nhật menu khi ngôn ngữ thay đổi
        const navItems = computed(() => getLocalizedMenuItems());
        
        // Kiểm tra xem item có được phép hiển thị không dựa trên quyền
        const hasPermissionToView = (item: any): boolean => {
            // Nếu không có yêu cầu quyền thì hiển thị mặc định
            if (!item.permission && !item.permissions && !item.roles) {
                return true;
            }
            
            // Kiểm tra theo quyền đơn lẻ
            if (item.permission) {
                return authStore.hasPermission(item.permission);
            }
            
            // Kiểm tra theo danh sách quyền (phải có tất cả)
            if (item.permissions) {
                return item.requireAllPermissions 
                    ? authStore.hasAllPermissions(item.permissions)
                    : authStore.hasAnyPermission(item.permissions);
            }
            
            // Kiểm tra theo vai trò
            if (item.roles) {
                return item.roles.some((role: string) => authStore.hasRole(role));
            }
            
            return true;
        };
        
        // Kiểm tra xem group có ít nhất một con item được phép hiển thị không
        const hasVisibleChildren = (item: any): boolean => {
            if (!item.items) return false;
            return item.items.some((child: any) => {
                if (child.items) {
                    return hasVisibleChildren(child);
                }
                return hasPermissionToView(child);
            });
        };

        const renderItem = (item: any) => {
            // Kiểm tra quyền trước khi render
            if (!hasPermissionToView(item)) {
                // Nếu là group, kiểm tra xem có con item nào được phép hiển thị không
                if (item.items && !hasVisibleChildren(item)) {
                    return null;
                }
                // Nếu không phải group thì không hiển thị
                if (!item.items) {
                    return null;
                }
            }
            
            if (item.items) {
                // Nếu không có con item nào được phép hiển thị thì không hiển thị group
                if (!hasVisibleChildren(item)) {
                    return null;
                }
                
                return h(
                    CNavGroup,
                    {
                        as: 'div',
                        compact: true,
                        ...(firstRender.value && {
                            visible: item.items.some((child: any) => isActiveItem(route, child)),
                        }),
                    },
                    {
                        togglerContent: () => [
                            // Thay thế CIcon bằng Material Icons
                            item.icon
                                ? h('span', { class: 'material-symbols-outlined nav-icon' }, item.icon)
                                : null,
                            item.name,
                        ],
                        default: () => item.items.filter(child => {
                            if (child.items) {
                                return hasVisibleChildren(child);
                            }
                            return hasPermissionToView(child);
                        }).map(renderItem),
                    },
                );
            }

            return item.to
                ? h(
                    RouterLink,
                    {
                        to: item.to,
                        custom: true,
                    },
                    {
                        default: (props: any) =>
                            h(
                                resolveComponent(item.component),
                                {
                                    active: props.isActive,
                                    as: 'div',
                                    href: props.href,
                                    onClick: props.navigate,
                                },
                                {
                                    default: () => [
                                        item.icon
                                            ? h('span', { class: 'material-symbols-outlined nav-icon' }, item.icon)
                                            : h('span', { class: 'nav-icon' }, h('span', { class: 'nav-icon-bullet' })),
                                        item.name,
                                        item.badge &&
                                            h(
                                                CBadge,
                                                {
                                                    class: 'ms-auto',
                                                    color: item.badge.color,
                                                },
                                                {
                                                    default: () => item.badge.text,
                                                },
                                            ),
                                    ],
                                },
                            ),
                    },
                  )
                : h(
                    resolveComponent(item.component),
                    {
                        as: 'div',
                    },
                    {
                        default: () => item.name,
                    },
                );
        };

        return () =>
            h(
                CSidebarNav,
                {
                    as: simplebar,
                },
                {
                    default: () => navItems.value.filter(item => {
                        if (item.items) {
                            return hasVisibleChildren(item);
                        }
                        return hasPermissionToView(item);
                    }).map(renderItem),
                },
            );
    },
});

export { AppSidebarNav };
