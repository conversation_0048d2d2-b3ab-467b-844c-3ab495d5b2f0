<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ProcessTransitionsCondition extends Model
{
    use HasUuids;
    
    protected $table = 'process_transitions_conditions';
    protected $primaryKey = 'id';

    protected $fillable = [
        'process_transition_id',
        'condition_id',
        'condition_status',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    /**
     * <PERSON><PERSON><PERSON> quan hệ với bảng conditions
     */
    public function condition()
    {
        return $this->belongsTo('App\Models\Condition', 'condition_id', 'id');
    }
}
