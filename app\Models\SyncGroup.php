<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class SyncGroup extends Model
{
    use HasUuids;
    protected $table = 'sync_groups';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'slug',
        'process_version_id',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
