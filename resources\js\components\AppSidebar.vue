<template>
    <CSidebar
        class="border-end"
        position="fixed"
        :unfoldable="sidebar.unfoldable"
        :visible="sidebar.visible"
        @visible-change="(value) => sidebar.toggleVisible(value)"
    >
        <CSidebarHeader class="border-bottom">
            <RouterLink custom to="/" v-slot="{ href, navigate }">
                <CSidebarBrand v-bind="$attrs" as="a" :href="href" @click="navigate">
                    <CCardImage custom-class-name="sidebar-brand-narrow" :src="logo" :height="32" />
                </CSidebarBrand>
            </RouterLink>
            <CCloseButton class="d-lg-none" dark @click="sidebar.toggleVisible()" />
        </CSidebarHeader>
        <AppSidebarNav />
        <CSidebarFooter class="border-top d-none d-lg-flex">
            <CSidebarToggler @click="sidebar.toggleUnfoldable()" />
        </CSidebarFooter>
    </CSidebar>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import { RouterLink } from 'vue-router';
    import logo from '@/assets/brand/logo.png';
    import { AppSidebarNav } from '@/components/AppSidebarNav';
    import { useSidebarStore } from '@/stores/sidebar';

    export default defineComponent({
        components: {
            RouterLink,
            AppSidebarNav,
        },
        setup() {
            const sidebar: any = useSidebarStore();

            return {
                sidebar,
                logo,
            };
        },
    });
</script>


