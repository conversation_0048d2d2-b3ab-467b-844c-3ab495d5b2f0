<template>
	<CForm class="row g-2">
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.name')}}
			</CFormLabel>
			<div class="fw-normal">
				{{ getStageData?.name || $t('common.no_data') }}
			</div>
		</CCol>
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.description')}}
			</CFormLabel>
			<div class="fw-normal">
				{{ getStageData?.description || $t('common.no_data') }}
			</div>
		</CCol>
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.approver')}}
			</CFormLabel>
			<div class="fw-normal">
				<div v-if="getStageData?.approvers && getStageData.approvers.length" class="d-flex flex-wrap">
					<div
						v-for="approver in getStageData?.approvers"
						:key="approver"
						class="badge bg-success me-1 mb-2"
					>
						{{ approver }}
					</div>
				</div>
				<div v-else>
					{{ $t('common.no_data') }}
				</div>
			</div>
		</CCol>
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.follower')}}
			</CFormLabel>
			<div class="fw-normal">
				<div v-if="getStageData?.followers && getStageData.followers.length" class="d-flex flex-wrap">
					<div
						v-for="follower in getStageData?.followers"
						:key="follower"
						class="badge bg-success me-1 mb-2"
					>
						{{ follower }}
					</div>
				</div>
				<div v-else>
					{{ $t('common.no_data') }}
				</div>
			</div>
		</CCol>
	</CForm>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useI18n } from "vue-i18n";

export default defineComponent({
	name: "StageView",
	
	props: {
		stageType: {
			type: String,
			required: true,
			validator: (value: string) => ['from_stage', 'to_stage', 'back_to_stage'].includes(value)
		},
		stageData: {
			type: Object,
			required: true
		}
	},
	
	setup(props) {
		const { t } = useI18n();
		
		// Computed property to get the correct stage data based on stage type
		const getStageData = computed(() => {
			switch (props.stageType) {
				case 'from_stage':
					return props.stageData.from_stage;
				case 'to_stage':
					return props.stageData.to_stage;
				case 'back_to_stage':
					return props.stageData.back_to_stage;
				default:
					return null;
			}
		});
		
		return {
			getStageData
		}
	}
});
</script>

<style scoped>
</style>
