<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|unique:roles,name',
            'permissions' => 'required|array',
            'permissions.*.id' => 'required|uuid|exists:permissions,id',
            'permissions.*.scope' => 'required|string',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Vui lòng nhập tên vai trò.',
            'name.string' => 'Tên vai trò phải là chuỗi.',
            'name.unique' => 'Tên vai trò đã tồn tại.',
            'permissions.required' => 'Vui lòng chọn quyền.',
            'permissions.array' => 'Quyền phải là mảng.',
            'permissions.*.id.required' => 'ID quyền không được bỏ trống.',
            'permissions.*.id.uuid' => 'ID quyền phải là UUID.',
            'permissions.*.id.exists' => 'ID quyền không tồn tại.',
            'permissions.*.scope.required' => 'Scope không được bỏ trống.',
            'permissions.*.scope.string' => 'Scope phải là chuỗi.',
        ];
    }
}
