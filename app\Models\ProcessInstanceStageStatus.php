<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;


class ProcessInstanceStageStatus extends Model
{
    use HasUuids;
    
    protected $table = 'process_instance_stage_status';
    protected $primaryKey = 'id';

    protected $fillable = [
        'job_id',
        'stage_id',
        'status',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    public function job()
    {
        return $this->belongsTo('App\Models\Job', 'job_id', 'id');
    }

    public function stage()
    {
        return $this->belongsTo('App\Models\Stage', 'stage_id', 'id');
    }
}
