# Dynamic Form Fields Components

Hệ thống component form động đư<PERSON>c tách ra từ JobAdd.vue để có thể tái sử dụng trong các component khác.

## Cấu trúc Components

### 1. **DynamicFormFields.vue** - Component chính
- Xử lý tất cả các loại field động
- Hỗ trợ validation với vee-validate và FormKit
- Emit events để component cha xử lý

### 2. **DynamicTable.vue** - Component xử lý TABLE fields
- Xử lý các field có type là TABLE
- Hỗ trợ thêm/xóa rows
- Tích hợp với DynamicFormFields

### 3. **useDynamicFormLogic.ts** - Composable chứa logic
- Chứa tất cả pure functions
- C<PERSON> thể tái sử dụng trong nhiều component

## Cách sử dụng trong component mới

### Bước 1: Import và setup

```vue
<script setup lang="ts">
import { reactive, computed } from 'vue'
import DynamicFormFields from '@/components/DynamicFormFields.vue'
import useDynamicFormLogic from '@/composables/useDynamicFormLogic'

// Import composable
const {
  converFormFields,
  initializeItem,
  initializeItemChildrens,
  formattedFormulaChildrenResults,
  calculateFormulaWithFormData,
  extractSumPlus,
  sumColumn
} = useDynamicFormLogic()

// State management
const state = reactive({
  dataFormFields: [], // Dữ liệu fields từ API
  formData: {} as any, // Dữ liệu form chính
  itemChildrens: {} as { [key: string]: any[] }, // Dữ liệu cho TABLE fields
  selectOptionDepartments: [] as any[],
  subColumnTableDescription: {} as any,
  subColumnTableOptionSelected: {} as any,
  subColumnTableDescriptionChildren: {} as any,
  subColumnTableOptionSelectedChildren: {} as any,
  maxFiles: 10,
  maxFileSize: '10MB',
  acceptedFileTypes: ['image/*', 'application/pdf']
})
</script>
```

### Bước 2: Computed fields

```vue
<script setup lang="ts">
// Transform fields và khởi tạo itemChildrens
const sortedFormFields = computed(() => {
  const transformedFields = converFormFields(state.dataFormFields, false)
  
  // Khởi tạo itemChildrens cho TABLE fields
  initializeItemChildrens(transformedFields, state.itemChildrens)
  
  return transformedFields
})

// Formula results cho main fields
const formulaResults = computed(() => {
  const results = {}
  sortedFormFields.value.forEach((field: any) => {
    if (field.type === 'FORMULA') {
      const formula = field.value
      if (formula.startsWith('=')) {
        results[field.name] = calculateFormulaWithFormData(field.value, results, state.formData)
      } else {
        // Xử lý SUMPLUS formula
        results[field.name] = sumColumn(field, state.itemChildrens)
      }
    }
  })
  return results
})

// Formatted formula results
const formattedFormulaResults = computed(() => {
  const results = {}
  for (const key in formulaResults.value) {
    results[key] = numberCommas(formulaResults.value[key])
    state.formData[key] = results[key]
  }
  return results
})
</script>
```

### Bước 3: Event handlers

```vue
<script setup lang="ts">
// Async functions cho options
const getOptionUsers = async (query: string) => {
  // Implement logic lấy users
  // return array of options
}

const getOptionColumnData = async (query: string, field: any) => {
  // Implement logic lấy column data
  // return array of options
}

// Event handlers
const updateFiles = (fileItemUploads: any, fieldName: string) => {
  state.formData[fieldName] = fileItemUploads.length > 0 
    ? fileItemUploads.map((fileItem: any) => fileItem.file) 
    : null
}

const updateFileChildrens = (fileItemUploads: any, itemChildren: object, fieldChildrenName: string) => {
  itemChildren[fieldChildrenName] = fileItemUploads.length > 0 
    ? fileItemUploads.map((fileItem: any) => fileItem.file) 
    : null
}

const addItem = (field: any) => {
  state.itemChildrens[field.name].push({ ...initializeItem(field.childrens) })
}

const removeItem = (field: any, itemIndex: number) => {
  state.itemChildrens[field.name].splice(itemIndex, 1)
}

const showSubColumnTable = (optionSelected: any, keyName: string) => {
  // Implement logic xử lý sub column table
}

const showSubColumnTableChildren = (optionSelected: any, itemIndex: number, keyName: string) => {
  // Implement logic xử lý sub column table children
}
</script>
```

### Bước 4: Template

```vue
<template>
  <Form @submit="handleSubmit" :validation-schema="schema">
    <DynamicFormFields
      :form-fields="sortedFormFields"
      :form-data="state.formData"
      :item-childrens="state.itemChildrens"
      :select-option-departments="state.selectOptionDepartments"
      :sub-column-table-description="state.subColumnTableDescription"
      :sub-column-table-option-selected="state.subColumnTableOptionSelected"
      :sub-column-table-description-children="state.subColumnTableDescriptionChildren"
      :sub-column-table-option-selected-children="state.subColumnTableOptionSelectedChildren"
      :formatted-formula-results="formattedFormulaResults"
      :max-files="state.maxFiles"
      :max-file-size="state.maxFileSize"
      :accepted-file-types="state.acceptedFileTypes"
      :get-option-users="getOptionUsers"
      :get-option-column-data="getOptionColumnData"
      @update-form-data="(key, value) => state.formData[key] = value"
      @update-files="updateFiles"
      @update-file-childrens="updateFileChildrens"
      @add-item="addItem"
      @remove-item="removeItem"
      @show-sub-column-table="showSubColumnTable"
      @show-sub-column-table-children="showSubColumnTableChildren"
    />
  </Form>
</template>
```

## Các function quan trọng từ composable

### 1. **converFormFields(dataFormFields, fieldTable)**
- Transform dữ liệu fields từ API thành format Vue
- `fieldTable`: true cho children fields, false cho main fields

### 2. **initializeItem(childrens)**
- Khởi tạo một item mới cho TABLE field
- Xử lý default values và formula setup

### 3. **initializeItemChildrens(transformedFields, itemChildrens)**
- Helper function khởi tạo tất cả itemChildrens cho TABLE fields
- Tự động gọi initializeItem cho mỗi TABLE field

### 4. **calculateFormulaWithFormData(formula, results, formData)**
- Tính toán công thức với formData và results context
- Logic giống với JobAddBackup.vue ban đầu
- Sử dụng cho main formula calculations

### 5. **extractSumPlus(str)**
- Bóc tách thông tin từ SUMPLUS formula
- Trả về [tableName, columnName] hoặc null
- Ví dụ: "(table1.column1)" → ["table1", "column1"]

### 6. **sumColumn(field, itemChildrens)**
- Tính tổng cột trong TABLE dựa trên SUMPLUS formula
- Sử dụng extractSumPlus để parse formula
- Trả về tổng giá trị số của cột

### 7. **formattedFormulaChildrenResults(nameKey, itemChildrens, formData)**
- Tính toán và format formula results cho TABLE children
- Kết hợp context từ formData và item data

## Lưu ý quan trọng

1. **State Management**: Mỗi component cần quản lý state riêng của mình
2. **Validation**: Cần setup validation schema riêng cho từng component
3. **API Integration**: Cần implement các async functions cho options
4. **File Upload**: Cần xử lý file upload logic riêng
5. **Formula Context**: Đảm bảo context đầy đủ cho formula calculations

## Ví dụ hoàn chỉnh

Xem JobAdd.vue để tham khảo implementation hoàn chỉnh.
