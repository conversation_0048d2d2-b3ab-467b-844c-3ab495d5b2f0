<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stages', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('name', 200);  // Tên giai đoạn
            $table->string('slug', 200);  // Slug giai đoạn
            $table->text('description')->nullable();  // Mô tả giai đoạn
            $table->json('approver');  // Người duyệt/ Người thực hiện
            $table->json('followers')->nullable();  // Người theo dõi
            $table->boolean('comment')->default(false)->comment('0 - Không yêu cầu, 1 - <PERSON><PERSON><PERSON> cầu nhập ý kiến');  // Y<PERSON>u cầu phải nhập ý kiến
            $table->uuid('process_version_id');  // Thuộc phiên bản quy trình
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('stages', function (Blueprint $table) {
            $table->index(['id', 'process_version_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stages');
    }
};
