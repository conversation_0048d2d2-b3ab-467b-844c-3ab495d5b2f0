<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fields', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('keyword', 50);  // Từ khóa hiển thị
            $table->string('display_name', 255);  // Tên trường VN
            $table->string('display_name_en', 255)->nullable();  // Tên trường EN
            $table->string('type', 50);  // Loại dữ liệu
            $table->text('default_value')->nullable();  // Giá trị mặc định
            $table->integer('required')->default(0)->comment('0- Không bắt buộc nhập, 1- <PERSON><PERSON><PERSON> buộc nhập');  // Không bắt buộc nhập
            $table->integer('order')->nullable();  // Thứ tự
            $table->integer('min_equal')->nullable();  // Ký tự min/ Giá trị min
            $table->integer('max_equal')->nullable();  // Ký tự max/ Giá trị max
            $table->uuid('stage_id');  // Thuộc giai đoạn
            $table->string('placeholder', 255)->nullable();  // Placeholder VN
            $table->string('placeholder_en', 255)->nullable();  // Placeholder EN
            $table->integer('column_width');  // Chiều rộng cột
            $table->uuid('form_id');  // Thuộc form
            $table->integer('not_edit')->default(0)->comment('0- Cho phép sửa giá trị, 1- Không cho phép sửa giá trị');  // Cho phép sửa giá trị
            $table->integer('multiple')->default(0)->comment('0- Không cho phép chọn nhiều giá trị, 1- Cho phép chọn nhiều giá trị');  //  Không cho phép chọn nhiều giá trị
            $table->json('options')->nullable();  // Giá trị lựa chọn
            $table->string('object_table', 50)->nullable();  // Bảng đối tượng
            $table->string('column_table', 50)->nullable();  // Tên cột hiển thị
            $table->json('sub_column_table')->nullable();  // Tên các cột phụ hiển thị
            $table->uuid('parent_id')->nullable();  // Thuộc trường
            $table->boolean('is_active')->default(true)->comment('0 - Không hoạt động, 1 - Hoạt động');  // Trạng thái hoạt động
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();  // Ngày tạo và cập nhật
        });

        // Tạo chỉ mục
        Schema::table('fields', function (Blueprint $table) {
            $table->index(['id', 'form_id', 'stage_id', 'create_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fields');
    }
};
