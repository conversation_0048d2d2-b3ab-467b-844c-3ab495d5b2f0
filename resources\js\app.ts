import { createApp } from 'vue'
import { createPinia } from 'pinia'
import axios from 'axios';
import apiService from './services/axios';

import App from './App.vue'
import router from './router'

import CoreuiVue from '@coreui/vue'
import CIcon from '@coreui/icons-vue'
import { iconsSet as icons } from '@/assets/icons'
import {createBootstrap} from 'bootstrap-vue-next'
import { plugin } from '@formkit/vue'
import formkitConfig from './formkit.config'

//i18n
import i18n from './lang/i18n';
// Attach i18n to window object for global access
// Sử dụng khai báo đã có trong hệ thống
window.i18n = i18n;
//material-icons
import 'material-icons/iconfont/material-icons.css';
//material-symbols
import 'material-symbols';

//bootstrap-vue-next
import 'bootstrap/dist/css/bootstrap.css'
import 'bootstrap-vue-next/dist/bootstrap-vue-next.css'
//vue-toast-notification
import 'vue-toast-notification/dist/theme-sugar.css';

// Import hàm checkLocale
import { checkLocale } from './utils/utils';

// Get language from localStorage or use 'vi' as default
const selectedLanguage = checkLocale(localStorage.getItem('app_language'));
axios.defaults.headers.common['Accept-Language'] = selectedLanguage;

const app = createApp(App)
app.use(createPinia())
app.use(router)
app.use(CoreuiVue)
app.provide('icons', icons)
app.component('CIcon', CIcon)
app.use(createBootstrap())
app.use(plugin, formkitConfig)

app.use(i18n)

app.mount('#app')
