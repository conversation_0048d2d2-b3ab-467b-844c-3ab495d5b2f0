<?php

// <PERSON><PERSON> báo namespace tương ứng với vị trí file
namespace App\Enums;

use Illuminate\Support\Str;

// Định nghĩa Enum với kiểu giá trị nền là string
enum ProcessStatus: string
{
    // Định nghĩa các trường hợp (cases) có thể có và giá trị chuỗi tương ứng
    case ALL = 'all'; // Tất cả
    case ACTIVE = 'active'; // Hoạt động
    case UNACTIVE = 'unactive'; // Không hoạt động
    case SAVE_DRAFT = 'save_draft'; // Lư<PERSON> nháp	

    public function label(): string
    {
        // 1. Tạo phần giữa của key từ tên lớp Enum (e.g., PermissionStatus -> permission_status)
        $enum_key = Str::snake(class_basename(static::class));

        // 2. Lấy phần cuối của key từ tên của case (e.g., ALL -> all)
        $case_key = strtolower($this->name);

        // 3. <PERSON>h<PERSON><PERSON> lại thành key dịch đầy đủ (e.g., "enums.permission_status.all")
        $translation_key = "enums.{$enum_key}.{$case_key}";

        // 4. Trả về bản dịch bằng hàm helper __()
        return __($translation_key);
    }

    /**
     * Tạo một instance Enum từ một giá trị chuỗi (string).
     *
     * @param string $status Chuỗi cần chuyển đổi ('all', 'active', 'unactive').
     * @return self
     */
    public static function fromString(string $status): self
    {
        return match (strtolower($status)) {
            'all' => self::ALL,
            'active' => self::ACTIVE,
            'unactive' => self::UNACTIVE,
            'save_draft' => self::SAVE_DRAFT,
        };
    }

    public function applyToQuery($query, string $column = 'status')
    {
        // Nếu case hiện tại là ALL, không làm gì cả, trả về query ban đầu.
        if ($this === self::ALL) {
            return $query;
        }

        // Nếu là các case khác, thêm điều kiện WHERE.
        return $query->where($column, $this->value);
    }
}