<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('department_subs', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->date('date_start'); // Ngày bắt đầu
            $table->date('date_end')->nullable(); // Ng<PERSON>y kết thúc
            $table->uuid('department_id');  // ID phòng ban
            $table->uuid('rank_id');  // ID chức vụ
            $table->uuid('job_position_id');  // ID vị trí công việc
            $table->uuid('user_id');  // ID người dùng
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('department_subs');
    }
};
