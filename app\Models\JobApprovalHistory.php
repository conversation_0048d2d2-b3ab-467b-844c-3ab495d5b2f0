<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Support\Facades\Auth;

class JobApprovalHistory extends Model
{
    use HasUuids;
    
    protected $table = 'job_approval_history';
    protected $primaryKey = 'id';

    protected $fillable = [
        'job_id',
        'stage_id',
        'user_id',
        'approved_list',
        'followers',
        'action_id',
        'date',
        'comment',
    ];
    
    protected $casts = [
        'approved_list' => 'array', 
        'followers' => 'array', 
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'date' => 'datetime',
    ];

    public function job()
    {
        return $this->belongsTo(SaveJob::class, 'job_id', 'id');
    }

    public function actor() 
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function stage() 
    {
        return $this->belongsTo(Stage::class, 'stage_id');
    }

    public function action() 
    {
        return $this->belongsTo(Action::class, 'action_id');
    }

    public function isUserInApprovedList(?User $user): bool
    {
        if (!$user) {
            return false;
        }
        return in_array($user->id, $this->approved_list ?? []);
    }

    // Helper methods
    public function canBeApprovedBy(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;
        
        return !$this->user_id && in_array($user->id, $this->approved_list ?? []);
    }
    
    public function isUserFollower(?User $user = null): bool
    {
        $user = $user ?: Auth::user();
        if (!$user) return false;
        
        return in_array($user->id, $this->followers ?? []);
    }
    
    public function isPending(): bool
    {
        return $this->user_id === null;
    }
    
    public function isApproved(): bool
    {
        return $this->user_id !== null;
    }
}
