import axios from 'axios';
import { checkLocale } from './utils';

// Tạo một đối tượng để phát sự kiện thay đổi ngôn ngữ
export const languageEventBus = {
    listeners: [] as Array<(lang: string) => void>,
    
    /**
     * Đăng ký một hàm lắng nghe sự kiện thay đổi ngôn ngữ
     * @param callback Hàm sẽ được gọi khi ngôn ngữ thay đổi
     */
    onLanguageChanged(callback: (lang: string) => void) {
        this.listeners.push(callback);
    },
    
    /**
     * Ph<PERSON>t sự kiện thay đổi ngôn ngữ
     * @param lang Ngôn ngữ mới
     */
    emit(lang: string) {
        this.listeners.forEach(callback => callback(lang));
    }
};

/**
 * Thay đổi ngôn ngữ của ứng dụng một cách nhất quán
 * @param lang Mã ngôn ngữ ('vi' hoặc 'en')
 * @returns Mã ngôn ngữ đã được chuẩn hóa
 */
export const changeAppLanguage = (lang: string): string => {
    // Chuẩn hóa ngôn ngữ
    const normalizedLang = checkLocale(lang);
    
    // Lưu vào localStorage
    localStorage.setItem('app_language', normalizedLang);
    
    // Cập nhật header của axios
    axios.defaults.headers.common['Accept-Language'] = normalizedLang;
    
    // Cập nhật i18n locale nếu có
    if (window.i18n && typeof window.i18n.global.locale !== 'undefined') {
        window.i18n.global.locale.value = normalizedLang;
        
        // Phát sự kiện thay đổi ngôn ngữ
        languageEventBus.emit(normalizedLang);
    }
    
    return normalizedLang;
};

/**
 * Lấy ngôn ngữ hiện tại của ứng dụng
 * @returns Mã ngôn ngữ hiện tại
 */
export const getCurrentLanguage = (): string => {
    return checkLocale(localStorage.getItem('app_language'));
};
