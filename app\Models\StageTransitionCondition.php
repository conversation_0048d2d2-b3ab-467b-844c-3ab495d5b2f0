<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class StageTransitionCondition extends Model
{
    use HasUuids;
    
    protected $table = 'stage_transition_conditions';
    protected $primaryKey = 'id';

    protected $fillable = [
        'stage_transition_id',
        'condition_id',
        'condition_status',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function condition()
    {
        return $this->belongsTo('App\Models\Condition', 'condition_id', 'id');
    }
}
