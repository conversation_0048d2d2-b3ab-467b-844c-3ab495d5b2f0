<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Traits\ActiveGlobalScopeTrait;

class Rank extends Model
{
    use ActiveGlobalScopeTrait, HasUuids;
    
    protected $table = 'ranks';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'order',
        'description',
        'is_active',
        'tenant_id',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
