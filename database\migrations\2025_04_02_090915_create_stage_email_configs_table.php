<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stage_email_configs', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('stage_id');  // ID giai đoạn
            $table->uuid('template_id');  // ID mẫu email
            $table->uuid('action_id');  // ID hành động
            $table->uuid('process_version_id');  // Thuộc phiên bản quy trình
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('stage_email_configs', function (Blueprint $table) {
            $table->index(['id', 'process_version_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stage_email_configs');
    }
};
