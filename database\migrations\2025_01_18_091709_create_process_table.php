<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('process', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('name', 200);  // Tên quy trình
            $table->string('description', 500)->nullable();  // <PERSON>ô tả quy trình
            $table->string('status', 20)->comment('active - Hoạt động, unactive - Không hoạt động, save_draft - Lưu nháp');  // Trạng thái quy trình
            $table->uuid('process_group_id')->nullable();  // Thuộc nhóm quy trình
            $table->uuid('tenant_id');  // Thuộc đơn vị
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('process', function (Blueprint $table) {
            $table->index(['id', 'process_group_id', 'tenant_id', 'create_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('process');
    }
};
