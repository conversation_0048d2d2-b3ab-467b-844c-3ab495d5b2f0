<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Permission extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'permissions';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'slug',
        'permission_group_id',
        'is_hidden',
        'is_active',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    /**
     * Quan hệ nhiều-nhiều với roles
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'permission_role');
    }

    public function permissionGroup()
    {
        return $this->belongsTo(PermissionGroup::class, 'permission_group_id');
    }

    public function createBy()
    {
        return $this->belongsTo(User::class, 'create_by');
    }
} 