<template>
    <CCol :xs="12">
        <BTabs no-body content-class="mt-3" v-model="state.tabIndex">
            <BTab :title="$t('workflow.email.create')" active>
                <Form ref="form" @submit="handleSubmitFormEmailTemplate" :validation-schema="schemaCreate()">
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.name') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataEmailTemplate.name"
                            name="name" 
                            type="text" 
                            class="form-control" 
                            maxlength="100" 
                            @change="handleInputNameAdd(dataEmailTemplate.name)"
                        />
                        <ErrorMessage
                            as="div"
                            name="name"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.slug') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataEmailTemplate.slug"
                            name="slug" 
                            type="text" 
                            class="form-control" 
                            maxlength="200" 
                            :readonly="true"
                        />
                        <ErrorMessage
                            as="div"
                            name="slug"
                            class="text-danger"
                        />
                        <div class="text-danger mb-3" v-if="checkDuplicateSlugAdd(dataEmailTemplate.slug)">
                            {{ $t('workflow.email.validate.duplicate_slug') }}
                        </div>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.condition') }}
                        </label>
                        <Multiselect
                            :placeholder="$t('workflow.choose')"
                            :close-on-select="false"
                            :searchable="true"
                            :object="true"
                            :can-clear="false"
                            v-model="dataEmailTemplate.condition"
                            :options="selectOptionConditions"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.from_email') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            name="from_email"
                            v-slot="{ field }"
                        >
                            <Multiselect
                                v-bind="field"
                                :placeholder="$t('workflow.choose')"
                                :close-on-select="false"
                                :searchable="true"
                                :object="true"
                                :can-clear="false"
                                v-model="dataEmailTemplate.from_email"
                                :options="state.selectOptionFromEmails"
                            >
                                <template v-slot:option="{ option }">
                                    <div class="custom-option">
                                        <div class="option-label mb-1">
                                            {{ option.label }}
                                        </div>
                                        <div class="option-description text-secondary">
                                            <small>
                                                <i>{{ option.description }}</i>
                                            </small>
                                        </div>
                                    </div>
                                </template>
                            </Multiselect>
                        </Field>
                        <ErrorMessage
                            as="div"
                            name="from_email"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.name_title') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            name="name_title" 
                            type="text" 
                            class="form-control" 
                            maxlength="200" 
                            v-model="dataEmailTemplate.name_title"
                            @focus="isDropdownVisible = true" 
                            @blur="handleBlurDropdownHide"
                        />
                        <ErrorMessage
                            as="div"
                            name="name_title"
                            class="text-danger"
                        />
                        <div class="dropdown" v-if="isDropdownVisible">
                            <BFormInput 
                                class="form-control mt-2" 
                                :placeholder="$t('search.title')" 
                                v-model="state.searchQueryField" 
                                @focus="isDropdownVisible = true" 
                                @blur="handleBlurDropdownHide"
                            />
                            <ul class="list-group mt-2">
                                <template v-if="filteredOptionFields.length > 0">
                                    <li 
                                        class="list-group-item" 
                                        v-for="(field, indexField) in filteredOptionFields" 
                                        :key="indexField" 
                                        @click="addOptionField(field)"
                                    >
                                        <p class="text-dark">{{ field.display_name }}</p>
                                        <p class="text-secondary"><i>&lbrace;{{ field.keyword }}&rbrace;</i></p>
                                    </li>
                                </template>
                                <template v-else>
                                    <li class="list-group-item">{{ $t('search.no_matching_records_found') }}</li>
                                </template>
                            </ul>
                        </div>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.to_emails') }}
                            <span class="text-danger">*</span>
                        </label>
                        <div class="d-flex align-items-center">
                            <Field 
                                name="to_emails"
                                v-slot="{ field }"
                            >
                                <Multiselect
                                    mode="tags"
                                    v-bind="field"
                                    v-model="dataEmailTemplate.to_emails"
                                    :placeholder="$t('workflow.choose')"
                                    :close-on-select="false"
                                    :filter-results="false"
                                    :resolve-on-load="false"
                                    :infinite="true"
                                    :limit="20"
                                    :clear-on-search="true"
                                    :searchable="true"
                                    :delay="0"
                                    :min-chars="0"
                                    :object="true"
                                    :options="async (query) => {
                                        return await debouncedGetOptionScopes(query)
                                    }"
                                    @open="debouncedGetOptionScopes('')"
                                    :can-clear="false"
                                >
                                    <template v-slot:option="{ option }">
                                        <div class="custom-option">
                                            <div class="option-label mb-1">
                                                {{ option.label }}
                                            </div>
                                            <div class="option-description text-secondary">
                                                <small>
                                                    <i>{{ option.description }}</i>
                                                </small>
                                            </div>
                                        </div>
                                    </template>
                                </Multiselect>
                            </Field>
                            <span class="ms-2 cursor-pointer" @click="toggleEmailCc">{{ $t('workflow.email.cc_emails') }}</span> 
                            <span class="ms-2 cursor-pointer" @click="toggleEmailBcc">{{ $t('workflow.email.bcc_emails') }}</span> 
                        </div>
                        <ErrorMessage
                            as="div"
                            name="to_emails"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3" v-if="state.activeEmailCc">
                        <label class="mb-1">
                            {{ $t('workflow.email.cc_emails') }}
                        </label>
                        <Multiselect
                            mode="tags"
                            v-model="dataEmailTemplate.cc_emails"
                            :placeholder="$t('workflow.choose')"
                            :close-on-select="false"
                            :filter-results="false"
                            :resolve-on-load="false"
                            :infinite="true"
                            :limit="20"
                            :clear-on-search="true"
                            :searchable="true"
                            :delay="0"
                            :min-chars="0"
                            :object="true"
                            :options="async (query) => {
                                return await debouncedGetOptionScopes(query)
                            }"
                            @open="debouncedGetOptionScopes('')"
                            :can-clear="false"
                        >
                            <template v-slot:option="{ option }">
                                <div class="custom-option">
                                    <div class="option-label mb-1">
                                        {{ option.label }}
                                    </div>
                                    <div class="option-description text-secondary">
                                        <small>
                                            <i>{{ option.description }}</i>
                                        </small>
                                    </div>
                                </div>
                            </template>
                        </Multiselect>
                    </CCol>
                    <CCol :xs="12" class="mb-3" v-if="state.activeEmailBcc">
                        <label class="mb-1">
                            {{ $t('workflow.email.bcc_emails') }}
                        </label>
                        <Multiselect
                            mode="tags"
                            v-model="dataEmailTemplate.bcc_emails"
                            :placeholder="$t('workflow.choose')"
                            :close-on-select="false"
                            :filter-results="false"
                            :resolve-on-load="false"
                            :infinite="true"
                            :limit="20"
                            :clear-on-search="true"
                            :searchable="true"
                            :delay="0"
                            :min-chars="0"
                            :object="true"
                            :options="async (query) => {
                                return await debouncedGetOptionScopes(query)
                            }"
                            @open="debouncedGetOptionScopes('')"
                            :can-clear="false"
                        >
                            <template v-slot:option="{ option }">
                                <div class="custom-option">
                                    <div class="option-label mb-1">
                                        {{ option.label }}
                                    </div>
                                    <div class="option-description text-secondary">
                                        <small>
                                            <i>{{ option.description }}</i>
                                        </small>
                                    </div>
                                </div>
                            </template>
                        </Multiselect>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.content') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataEmailTemplate.content"
                            name="content"
                            as="textarea"
                            class="form-control" 
                            :maxlength="20000" 
                            rows="5"
                            @focus="isContentDropdownVisible = true" 
                            @blur="handleBlurContentDropdownHide"
                        />
                        <ErrorMessage
                            as="div"
                            name="content"
                            class="text-danger"
                        />
                        <div class="dropdown" v-if="isContentDropdownVisible">
                            <BFormInput 
                                class="form-control mt-2" 
                                :placeholder="$t('search.title')" 
                                v-model="state.searchContentQueryField" 
                                @focus="isContentDropdownVisible = true" 
                                @blur="handleBlurContentDropdownHide"
                            />
                            <ul class="list-group mt-2">
                                <template v-if="filteredContentOptionFields.length > 0">
                                    <li 
                                        class="list-group-item" 
                                        v-for="(field, indexField) in filteredContentOptionFields" 
                                        :key="indexField" 
                                        @click="addContentOptionField(field)"
                                    >
                                        <p class="text-dark">{{ field.display_name }}</p>
                                        <p class="text-secondary"><i>&lbrace;{{ field.keyword }}&rbrace;</i></p>
                                    </li>
                                </template>
                                <template v-else>
                                    <li class="list-group-item">{{ $t('search.no_matching_records_found') }}</li>
                                </template>
                            </ul>
                        </div>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.email.files') }}
                        </label>
                        <FilePond
                            :files="dataEmailTemplate.files"
                            @updatefiles="(fileItemUploads) => updateFiles(fileItemUploads)"
                            @addfile="onAddFiles"
                            className="file-pond"
                            :labelIdle="$t('validate_field.file_upload.label_idle')"
                            :allowMultiple="true"
                            :maxFiles="state.maxFiles"
                            :maxFileSize="state.maxFileSize"
                            :acceptedFileTypes="state.acceptedFileTypes"
                            :labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
                            :labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
                            :fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
                            :labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
                            :instantUpload="false"
                            name="files"
                            ref="files"
                            credits="false"
                            allow-reorder="true"
                            item-insert-location="after"
                            image-preview-min-height="60"
                            image-preview-max-height="60"
                        />
                    </CCol>
                    <CCardFooter>
                        <div class="d-flex justify-content-end">
                            <CButton 
                                type="button"
                                class="btn btn-light border m-1"
                                @click="closeFormEmailTemplate"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.email.close') }}
                                </span>
                            </CButton>
                            <CButton 
                                type="submit"
                                class="btn btn-primary m-1"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.email.save_update') }}
                                </span>
                            </CButton>
                        </div>
                    </CCardFooter>
                </Form>
            </BTab>
            <BTab :title="$t('workflow.email.list')">
                <CTable align="middle" responsive>
                    <table class="table table-hover">
                        <tbody v-if="listDataEmailTemplates.length > 0">
                            <tr 
                                v-for="(emailTemplate, index) in listDataEmailTemplates" 
                                :key="index"
                            >
                                <td class="align-middle">{{ emailTemplate.value.name }}</td> 
                                <td class="align-middle col-sm-1 table__td--action">
                                    <svg @click="handleEditEmailTemplate(index, emailTemplate.value)"  class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M14.06 9.02l.92.92L5.92 19H5v-.92l9.06-9.06M17.66 3c-.25 0-.51.1-.7.29l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.2-.2-.45-.29-.71-.29zm-3.6 3.19L3 17.25V21h3.75L17.81 9.94l-3.75-3.75z"/>
                                    </svg>    
                                    <svg @click="removeEmailTemplate(index)" class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                                    </svg>  
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr>
                                <td colspan="2" class="align-middle text-center">
                                    {{ $t('search.no_matching_records_found') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </CTable>
                <BAccordion v-if="state.activeTabEdit">
                    <BAccordionItem :title="$t('workflow.email.edit')" visible>
                        <Form ref="form" @submit="handleSubmitEditEmailTemplate" :validation-schema="schemaEdit()">
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.name') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.emailTemplateDetail.name"
                                    name="name" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="100" 
                                    @change="handleInputNameEdit(state.emailTemplateDetail.name)"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="name"
                                    class="text-danger"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.slug') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.emailTemplateDetail.slug"
                                    name="slug" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="200" 
                                    :readonly="true"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="slug"
                                    class="text-danger"
                                />
                                <div class="text-danger mb-3" v-if="checkDuplicateSlugEdit(state.emailTemplateDetail.slug, state.indexEdit)">
                                    {{ $t('workflow.email.validate.duplicate_slug') }}
                                </div>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.condition') }}
                                </label>
                                <Multiselect
                                    :placeholder="$t('workflow.choose')"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :object="true"
                                    :can-clear="false"
                                    v-model="state.emailTemplateDetail.condition"
                                    :options="selectOptionConditions"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.from_email') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Multiselect
                                    :placeholder="$t('workflow.choose')"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :object="true"
                                    :can-clear="false"
                                    v-model="state.emailTemplateDetail.from_email"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.name_title') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    name="name_title" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="200" 
                                    v-model="state.emailTemplateDetail.name_title"
                                    @focus="isDropdownVisible = true" 
                                    @blur="handleBlurDropdownHide"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="name_title"
                                    class="text-danger"
                                />
                                <div class="dropdown" v-if="isDropdownVisible">
                                    <BFormInput 
                                        class="form-control mt-2" 
                                        :placeholder="$t('search.title')" 
                                        v-model="state.searchQueryField" 
                                        @focus="isDropdownVisible = true" 
                                        @blur="handleBlurDropdownHide"
                                    />
                                    <ul class="list-group mt-2">
                                        <template v-if="filteredOptionFields.length > 0">
                                            <li 
                                                class="list-group-item" 
                                                v-for="(field, indexField) in filteredOptionFields" 
                                                :key="indexField" 
                                                @click="addOptionFieldEdit(field)"
                                            >
                                                <p class="text-dark">{{ field.display_name }}</p>
                                                <p class="text-secondary"><i>&lbrace;{{ field.keyword }}&rbrace;</i></p>
                                            </li>
                                        </template>
                                        <template v-else>
                                            <li class="list-group-item">{{ $t('search.no_matching_records_found') }}</li>
                                        </template>
                                    </ul>
                                </div>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.to_emails') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="d-flex align-items-center">
                                    <Field 
                                        name="to_emails"
                                        v-slot="{ field }"
                                    >
                                        <Multiselect
                                            mode="tags"
                                            v-bind="field"
                                            v-model="state.emailTemplateDetail.to_emails"
                                            :placeholder="$t('workflow.choose')"
                                            :close-on-select="false"
                                            :filter-results="false"
                                            :resolve-on-load="false"
                                            :infinite="true"
                                            :limit="20"
                                            :clear-on-search="true"
                                            :searchable="true"
                                            :delay="0"
                                            :min-chars="0"
                                            :object="true"
                                            :options="async (query) => {
                                                return await debouncedGetOptionScopes(query)
                                            }"
                                            @open="debouncedGetOptionScopes('')"
                                            :can-clear="false"
                                        >
                                            <template v-slot:option="{ option }">
                                                <div class="custom-option">
                                                    <div class="option-label mb-1">
                                                        {{ option.label }}
                                                    </div>
                                                    <div class="option-description text-secondary">
                                                        <small>
                                                            <i>{{ option.description }}</i>
                                                        </small>
                                                    </div>
                                                </div>
                                            </template>
                                        </Multiselect>
                                    </Field>
                                    <span class="ms-2 cursor-pointer" @click="toggleEmailCc">{{ $t('workflow.email.cc_emails') }}</span> 
                                    <span class="ms-2 cursor-pointer" @click="toggleEmailBcc">{{ $t('workflow.email.bcc_emails') }}</span> 
                                </div>
                                <ErrorMessage
                                    as="div"
                                    name="to_emails"
                                    class="text-danger"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3" v-if="state.activeEmailCc">
                                <label class="mb-1">
                                    {{ $t('workflow.email.cc_emails') }}
                                </label>
                                <Multiselect
                                    mode="tags"
                                    v-model="state.emailTemplateDetail.cc_emails"
                                    :placeholder="$t('workflow.choose')"
                                    :close-on-select="false"
                                    :filter-results="false"
                                    :resolve-on-load="false"
                                    :infinite="true"
                                    :limit="20"
                                    :clear-on-search="true"
                                    :searchable="true"
                                    :delay="0"
                                    :min-chars="0"
                                    :object="true"
                                    :options="async (query) => {
                                        return await debouncedGetOptionScopes(query)
                                    }"
                                    @open="debouncedGetOptionScopes('')"
                                    :can-clear="false"
                                >
                                    <template v-slot:option="{ option }">
                                        <div class="custom-option">
                                            <div class="option-label mb-1">
                                                {{ option.label }}
                                            </div>
                                            <div class="option-description text-secondary">
                                                <small>
                                                    <i>{{ option.description }}</i>
                                                </small>
                                            </div>
                                        </div>
                                    </template>
                                </Multiselect>
                            </CCol>
                            <CCol :xs="12" class="mb-3" v-if="state.activeEmailBcc">
                                <label class="mb-1">
                                    {{ $t('workflow.email.bcc_emails') }}
                                </label>
                                <Multiselect
                                    mode="tags"
                                    v-model="state.emailTemplateDetail.bcc_emails"
                                    :placeholder="$t('workflow.choose')"
                                    :close-on-select="false"
                                    :filter-results="false"
                                    :resolve-on-load="false"
                                    :infinite="true"
                                    :limit="20"
                                    :clear-on-search="true"
                                    :searchable="true"
                                    :delay="0"
                                    :min-chars="0"
                                    :object="true"
                                    :options="async (query) => {
                                        return await debouncedGetOptionScopes(query)
                                    }"
                                    @open="debouncedGetOptionScopes('')"
                                    :can-clear="false"
                                >
                                    <template v-slot:option="{ option }">
                                        <div class="custom-option">
                                            <div class="option-label mb-1">
                                                {{ option.label }}
                                            </div>
                                            <div class="option-description text-secondary">
                                                <small>
                                                    <i>{{ option.description }}</i>
                                                </small>
                                            </div>
                                        </div>
                                    </template>
                                </Multiselect>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.content') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.emailTemplateDetail.content"
                                    name="content"
                                    as="textarea"
                                    class="form-control" 
                                    :maxlength="20000" 
                                    rows="5"
                                    @focus="isContentDropdownVisible = true" 
                                    @blur="handleBlurContentDropdownHide"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="content"
                                    class="text-danger"
                                />
                                <div class="dropdown" v-if="isContentDropdownVisible">
                                    <BFormInput 
                                        class="form-control mt-2" 
                                        :placeholder="$t('search.title')" 
                                        v-model="state.searchContentQueryField" 
                                        @focus="isContentDropdownVisible = true" 
                                        @blur="handleBlurContentDropdownHide"
                                    />
                                    <ul class="list-group mt-2">
                                        <template v-if="filteredContentOptionFields.length > 0">
                                            <li 
                                                class="list-group-item" 
                                                v-for="(field, indexField) in filteredContentOptionFields" 
                                                :key="indexField" 
                                                @click="addContentOptionFieldEdit(field)"
                                            >
                                                <p class="text-dark">{{ field.display_name }}</p>
                                                <p class="text-secondary"><i>&lbrace;{{ field.keyword }}&rbrace;</i></p>
                                            </li>
                                        </template>
                                        <template v-else>
                                            <li class="list-group-item">{{ $t('search.no_matching_records_found') }}</li>
                                        </template>
                                    </ul>
                                </div>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.email.files') }}
                                </label>
                                <FilePond
                                    :files="state.emailTemplateDetail.files"
                                    @updatefiles="(fileItemUploads) => updateFileEdits(fileItemUploads)"
                                    className="file-pond"
                                    :labelIdle="$t('validate_field.file_upload.label_idle')"
                                    :allowMultiple="true"
                                    :maxFiles="state.maxFiles"
                                    :maxFileSize="state.maxFileSize"
                                    :acceptedFileTypes="state.acceptedFileTypes"
                                    :labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
                                    :labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
                                    :fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
                                    :labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
                                    :instantUpload="false"
                                    name="files"
                                    ref="files"
                                    credits="false"
                                    allow-reorder="true"
                                    item-insert-location="after"
                                    image-preview-min-height="60"
                                    image-preview-max-height="60"
                                />
                            </CCol>
                            <CCardFooter>
                                <div class="d-flex justify-content-end">
                                    <CButton 
                                        type="button"
                                        class="btn btn-light border m-1"
                                        @click="closeEditEmailTemplate"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.email.close') }}
                                        </span>
                                    </CButton>
                                    <CButton 
                                        type="submit"
                                        class="btn btn-primary m-1"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.email.save_update') }}
                                        </span>
                                    </CButton>
                                </div>
                            </CCardFooter>
                        </Form>
                    </BAccordionItem>
                </BAccordion>
            </BTab>
        </BTabs>
    </CCol>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import Multiselect from '@vueform/multiselect';
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import cloneDeep from 'lodash/cloneDeep'
import debounce from 'lodash.debounce';
import useOptions from '@/composables/option';
import  { generateStandardSlug } from "@/utils/utils";
import { WORKFLOWS } from "@/constants/constants";
import vueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize);

export default defineComponent({
    name: 'EmailTemplateAdd',
    emits: ['close-modal-email-template', 'reset-modal-email-template', 'add-email-template', 'edit-email-template', 'remove-email-template'],

    components: {
        Multiselect,
        Form,
		Field,
		ErrorMessage,
        FilePond
    },

    props: {
        dataEmailTemplate: {
            type: Object,
            default: {},
            required: true,
        },
        listDataEmailTemplates: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
        fieldSetups: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
        fieldCreateds: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        }, 
        selectOptionConditions: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
        dataFlowTransitions: {
            type: Array as () => Array<any>, 
            default: [],
            required: false,
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const isDropdownVisible = ref(false);
        const isContentDropdownVisible = ref(false);

        const schemaCreate = () => {
            let schemaForm = yup.object().shape({});

            schemaForm = schemaForm.shape({
                name: yup.string()
                    .required(`${t('workflow.email.name')} ${t('workflow.email.validate.required')}`),
                slug: yup.string()
                    .required(`${t('workflow.email.slug')} ${t('workflow.email.validate.required')}`),
                name_title: yup.string()
                    .required(`${t('workflow.email.name_title')} ${t('workflow.email.validate.required')}`),
                content: yup.string()
                    .required(`${t('workflow.email.content')} ${t('workflow.email.validate.required')}`),
                to_emails: yup.array()
                    .min(1, `${t('workflow.email.to_emails')} ${t('workflow.email.validate.required')}`)
                    .required(`${t('workflow.email.to_emails')} ${t('workflow.email.validate.required')}`),
                from_email: yup.object()
                    .required(`${t('workflow.email.from_email')} ${t('workflow.email.validate.required')}`)
                    .typeError(`${t('workflow.email.from_email')} ${t('workflow.email.validate.required')}`),
            });

            return schemaForm;
        }

        const schemaEdit = () => {
            let schemaForm = yup.object().shape({});

            schemaForm = schemaForm.shape({
                name: yup.string()
                    .required(`${t('workflow.email.name')} ${t('workflow.email.validate.required')}`),
                slug: yup.string()
                    .required(`${t('workflow.email.slug')} ${t('workflow.email.validate.required')}`),
                name_title: yup.string()
                    .required(`${t('workflow.email.name_title')} ${t('workflow.email.validate.required')}`),
                content: yup.string()
                    .required(`${t('workflow.email.content')} ${t('workflow.email.validate.required')}`),
                to_emails: state.emailTemplateDetail.to_emails?.length ? yup.array().nullable() : yup.array()
                    .min(1, `${t('workflow.email.to_emails')} ${t('workflow.email.validate.required')}`)
                    .required(`${t('workflow.email.to_emails')} ${t('workflow.email.validate.required')}`),
            });

            return schemaForm;
        }

        const state = reactive({
			maxFiles: 50,
			maxFileSize: '5MB',
			acceptedFileTypes: [
				'image/*',
				'application/pdf', 
				'application/msword', 
				'application/vnd.ms-excel', 
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'text/plain' 
			],
            tabIndex: 0,
            activeTabEdit: false,
            indexEdit: null as any,
            emailTemplateDetail: {} as any,
            activeEmailCc: false,
            activeEmailBcc: false,
            selectOptionFields: [] as Array<any>,
            searchQueryField: '',
            searchContentQueryField: '',
            selectOptionFromEmails: [
				{ label: '<EMAIL>', description: `${t('workflow.option_from_email.email_desc')}`, value: '<EMAIL>' },
			] as Array<any>,
            selectOptionSystemDefaults: [
				{ label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
                { label: `${t('workflow.option_system_default.user')}`, description: `${t('workflow.option_system_default.user_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.USER_ID },
                { label: `${t('workflow.option_system_default.approve_by')}`, description: `${t('workflow.option_system_default.approve_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.APPROVE_BY_ID },
			] as Array<any>,
		});

        const closeFormEmailTemplate = () => {
            emit('close-modal-email-template');
		}

        const updateFiles = (fileItemUploads: any) => {
			props.dataEmailTemplate.files = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

        const updateFileEdits = (fileItemUploads: any) => {
			state.emailTemplateDetail.files = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

        const onAddFiles = (error: any, file: any) => {
			if (error) {
				props.dataEmailTemplate.files = props.dataEmailTemplate.files.filter((item: any) => item.name !== file.filename);
				$toast.open({
					message: `${error.main} - ${error.sub}`,
					type: "warning",
					duration: 10000,
					dismissible: true,
					position: "bottom-right",
				});
			}
		};

        const handleSubmitFormEmailTemplate = async () => {
            if (!checkDuplicateSlugAdd(props.dataEmailTemplate.slug)) {
                emit("add-email-template", props.dataEmailTemplate);
                emit('reset-modal-email-template');
                state.tabIndex = 1;
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const editEmailTemplate = async (index: number, emailTemplate: object): Promise<void> => {
            state.indexEdit = index;
            // Check if emailTemplate has 'id' key to determine if it needs formatting
            if ('id' in emailTemplate) {
                // Format API data to form data structure
                state.emailTemplateDetail = await formatApiDataToFormData(emailTemplate);
            } else {
                // Use existing form data structure
                state.emailTemplateDetail = cloneDeep(emailTemplate);
            }

            state.activeTabEdit = true;
		}

        // Wrapper function for template usage
        const handleEditEmailTemplate = (index: number, emailTemplate: object): void => {
            editEmailTemplate(index, emailTemplate).catch(error => {
                console.error('Error editing email template:', error);
            });
        }

        const formatApiDataToFormData = async (apiData: any) => {
            if (apiData.formatted === undefined) {
                // Tìm tất cả email_template trong props.dataFlowTransitions từ apiData.id và loại bỏ những email_template giống nhau
                const findEmailTemplatesFromdataFlowTransitions = (targetId: string) => {
                    const emailTemplates: any[] = [];
                    const seenTemplates = new Set<string>();

                    if (Array.isArray(props.dataFlowTransitions)) {
                        props.dataFlowTransitions.forEach((setupProcess: any) => {
                            // Kiểm tra email_template (mảng)
                            if (Array.isArray(setupProcess.email_template)) {
                                setupProcess.email_template.forEach((template: any) => {
                                    if (template && template.id === targetId) {
                                        const templateKey = template.id;
                                        if (!seenTemplates.has(templateKey)) {
                                            seenTemplates.add(templateKey);
                                            emailTemplates.push(template);
                                        }
                                    }
                                });
                            }

                            // Kiểm tra email_template_back (mảng)
                            if (Array.isArray(setupProcess.email_template_back)) {
                                setupProcess.email_template_back.forEach((template: any) => {
                                    if (template && template.id === targetId) {
                                        const templateKey = template.id;
                                        if (!seenTemplates.has(templateKey)) {
                                            seenTemplates.add(templateKey);
                                            emailTemplates.push(template);
                                        }
                                    }
                                });
                            }
                        });
                    }

                    return emailTemplates;
                };

                // Tìm các email templates từ dataFlowTransitions
                const foundEmailTemplates = findEmailTemplatesFromdataFlowTransitions(apiData.id);
                // Lấy tất cả conditions từ foundEmailTemplates và loại bỏ trùng lặp
                const extractConditionsFromEmailTemplates = (emailTemplates: any[]) => {
                    const conditions: any[] = [];
                    const seenConditions = new Set<string>();

                    emailTemplates.forEach((template: any) => {
                        // Kiểm tra conditions (mảng)
                        if (Array.isArray(template.condition)) {
                            template.condition.forEach((condition: any) => {
                                if (condition) {
                                    const conditionKey = condition.id;
                                    if (!seenConditions.has(conditionKey)) {
                                        seenConditions.add(conditionKey);
                                        conditions.push(condition);
                                    }
                                }
                            });
                        }
                    });

                    return conditions;
                };

                // Lấy conditions từ foundEmailTemplates
                const foundConditions = extractConditionsFromEmailTemplates(foundEmailTemplates);
                // Find condition option from selectOptionConditions
                // Kiểm tra option.value.id với id của foundConditions
                const conditionOption = props.selectOptionConditions.find((option: any) => {
                    // Kiểm tra với id của foundConditions
                    return foundConditions.some((condition: any) =>
                        condition.id && option.value.id === condition.id
                    );
                });

                // Find from_email option from selectOptionFromEmails
                const fromEmailOption = state.selectOptionFromEmails.find((option: any) =>
                    option.value === apiData.from_email
                );

                // Format to_emails array using getOptionProcessScopes
                const formatEmailArray = async (emailArray: string[] | null) => {
                    if (!emailArray) return [];

                    // Get all available options from getOptionProcessScopes
                    const dataOptionProcessScope = await getOptionProcessScopes('');
                    const systemOption = dataOptionProcessScope.filter((item: any) =>
                        emailArray.includes(item.value)
                    );

                    return emailArray.map((email: string) => {
                        const foundOption = systemOption.find((option: any) =>
                            option.value === email
                        );
                        return foundOption || { label: email, value: email };
                    });
                };

                return {
                    id: apiData.id,
                    name: apiData.name,
                    slug: apiData.slug,
                    condition: conditionOption || null,
                    from_email: fromEmailOption ? (fromEmailOption.label ? fromEmailOption : { label: apiData.from_email, value: apiData.from_email }) : apiData.from_email,
                    name_title: apiData.name_title,
                    to_emails: Array.isArray(apiData.to_emails) && apiData.to_emails.some((email: any) => email.label)
                        ? apiData.to_emails
                        : await formatEmailArray(apiData.to_emails),
                    cc_emails: Array.isArray(apiData.cc_emails) && apiData.cc_emails.some((email: any) => email.label)
                        ? apiData.cc_emails
                        : await formatEmailArray(apiData.cc_emails),
                    bcc_emails: Array.isArray(apiData.bcc_emails) && apiData.bcc_emails.some((email: any) => email.label)
                        ? apiData.bcc_emails
                        : await formatEmailArray(apiData.bcc_emails),
                    content: apiData.content,
                    files: apiData.files ? formatFilesForFilePond(apiData.files) : [],
                    formatted: true,
                };
            }
            apiData.files = formatFilesForFilePond(apiData.files);

            return apiData;
        }

        const formatFilesForFilePond = (files: any[]) => {
            return files.map((file: any) => {
                if (typeof file._relativePath === 'undefined') {
                    return {
                        source: typeof file === 'object' ? file.name : file,
                        options: {
                            type: 'local',
                            file: {
                                name: typeof file === 'object' ? file.name : file,
                                size: 0,
                            },
                        }
                    };
                }
                return file;
            });
        }

        const removeEmailTemplate = (index: number): void => {
            const slugValue = props.listDataEmailTemplates[index].value.slug;
			props.listDataEmailTemplates.splice(index, 1);
            emit("remove-email-template", props.listDataEmailTemplates, slugValue);
            closeEditEmailTemplate();
		}

        const closeEditEmailTemplate = () => {
            state.activeTabEdit = false;
        }

        const handleSubmitEditEmailTemplate = async () => {
            if (!checkDuplicateSlugEdit(state.emailTemplateDetail.slug, state.indexEdit)) {
                const slugValue = props.listDataEmailTemplates[state.indexEdit].value.slug;
                emit("edit-email-template", state.emailTemplateDetail, state.indexEdit, slugValue);
                closeEditEmailTemplate();
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const toggleEmailCc = () => {
            state.activeEmailCc = !state.activeEmailCc;
        }

        const toggleEmailBcc = () => {
            state.activeEmailBcc = !state.activeEmailBcc;
        }
        
        const filteredOptionFields = computed(() => {
            // Lấy tất cả các key trong children của các item có type là TABLE
            const tableChildKeys = getCombinedFields()
                .filter((item: any) => item.type === 'TABLE' && item.childrens)
                .flatMap((item: any) => item.childrens.map((child: any) => child.value));
            // Lọc các item không có type là table hoặc file và không có key nằm trong tableKeys
            state.selectOptionFields = getCombinedFields().filter((item: any) => {
                if (item.type === 'TABLE' || item.type === 'FILEUPLOAD') {
                    return false; // Loại bỏ item có type là TABLE hoặc FILEUPLOAD
                }

                if (tableChildKeys.includes(item.keyword)) {
                    return false; // Loại bỏ item có keyword nằm trong tableKeys
                }

                return true; // Giữ lại các item khác
            }); 

            return state.selectOptionFields.filter(field => 
                field.display_name.toLowerCase().includes(state.searchQueryField.toLowerCase())
            );
        });

        const filteredContentOptionFields = computed(() => {
            // Lấy tất cả các key trong children của các item có type là TABLE
            const tableChildKeys = getCombinedFields()
                .filter((item: any) => item.type === 'TABLE' && item.childrens)
                .flatMap((item: any) => item.childrens.map((child: any) => child.value));
            // Lọc các item không có type là table hoặc file và không có key nằm trong tableKeys
            const selectOptionFields = getCombinedFields().filter((item: any) => {
                if (item.type === 'TABLE' || item.type === 'FILEUPLOAD') {
                    return false; // Loại bỏ item có type là TABLE hoặc FILEUPLOAD
                }

                if (tableChildKeys.includes(item.keyword)) {
                    return false; // Loại bỏ item có keyword nằm trong tableKeys
                }

                return true; // Giữ lại các item khác
            }); 

            return selectOptionFields.filter(field => 
                field.display_name.toLowerCase().includes(state.searchContentQueryField.toLowerCase())
            );
        });

        const addOptionField = (optionField: any) => {
            // Thêm tùy chọn vào ô input hiện tại mà không có dấu phẩy
            props.dataEmailTemplate.name_title += (props.dataEmailTemplate.name_title ? ' ' : '') + `{${optionField.keyword}}`; // Thêm một khoảng trắng nếu ô không rỗng
            state.searchQueryField = ''; 
            isDropdownVisible.value = false;
        };

        const handleBlurDropdownHide = (event: any) => {
            state.searchQueryField = ''; 
            // Kiểm tra xem người dùng có nhấp vào một tùy chọn hay không
            setTimeout(() => {
                if (!event.relatedTarget || (!event.relatedTarget.classList.contains('list-group-item') && !event.relatedTarget.classList.contains('form-control'))) {
                    isDropdownVisible.value = false; // Ẩn dropdown khi mất tiêu điểm
                }
            }, 300); // Thêm một chút thời gian để cho phép sự kiện click xảy ra
        };

        const addOptionFieldEdit = (optionField: any) => {
            // Thêm tùy chọn vào ô input hiện tại mà không có dấu phẩy
            state.emailTemplateDetail.name_title += (state.emailTemplateDetail.name_title ? ' ' : '') + `{${optionField.keyword}}`; // Thêm một khoảng trắng nếu ô không rỗng
            state.searchQueryField = ''; 
            isDropdownVisible.value = false;
        };

        const addContentOptionField = (optionField: any) => {
            // Thêm tùy chọn vào ô input hiện tại mà không có dấu phẩy
            props.dataEmailTemplate.content += (props.dataEmailTemplate.content ? ' ' : '') + `{${optionField.keyword}}`; // Thêm một khoảng trắng nếu ô không rỗng
            state.searchContentQueryField = ''; 
            isContentDropdownVisible.value = false;
        };

        const handleBlurContentDropdownHide = (event: any) => {
            state.searchContentQueryField = ''; 
            // Kiểm tra xem người dùng có nhấp vào một tùy chọn hay không
            setTimeout(() => {
                if (!event.relatedTarget || (!event.relatedTarget.classList.contains('list-group-item') && !event.relatedTarget.classList.contains('form-control'))) {
                    isContentDropdownVisible.value = false; // Ẩn dropdown khi mất tiêu điểm
                }
            }, 300); // Thêm một chút thời gian để cho phép sự kiện click xảy ra
        };

        const addContentOptionFieldEdit = (optionField: any) => {
            // Thêm tùy chọn vào ô input hiện tại mà không có dấu phẩy
            state.emailTemplateDetail.content += (state.emailTemplateDetail.content ? ' ' : '') + `{${optionField.keyword}}`; // Thêm một khoảng trắng nếu ô không rỗng
            state.searchContentQueryField = ''; 
            isContentDropdownVisible.value = false;
        };

        const { getScopes } = useOptions();

        const getCombinedFields = () => {
            return [...props.fieldSetups, ...props.fieldCreateds];
        };

        const getOptionProcessScopes = async (query: string) => {
            let result = await getScopes(query);
            if (Array.isArray(result) && result.length > 0) {
                // Kết hợp các option mặc định với kết quả trả về từ API`
                return [...state.selectOptionSystemDefaults, ...result];
            }

            // Nếu không có kết quả từ API, chỉ trả về các option mặc định
            return [...state.selectOptionSystemDefaults];
		}

		const debouncedGetOptionScopes = debounce(getOptionProcessScopes, 500);

        const handleInputNameAdd = async (valueName: any) => {
            if (!valueName) {
                props.dataEmailTemplate.slug = '';
			} else {
    			props.dataEmailTemplate.slug = generateStandardSlug(valueName);
			}
		}

        const handleInputNameEdit = async (valueName: any) => {
            if (!valueName) {
                state.emailTemplateDetail.slug = '';
			} else {
    			state.emailTemplateDetail.slug = generateStandardSlug(valueName);
			}
		}

        const checkDuplicateSlugAdd = (slug: string): boolean => {
            return props.listDataEmailTemplates.some((item: any) => item.value.slug === slug);
        }

        const checkDuplicateSlugEdit = (slug: string, indexEdit: number): boolean => {
            return props.listDataEmailTemplates.some((item: any, index: number) => {
                // Bỏ qua kiểm tra nếu là chính slug hiện tại (indexEdit)
                if (index === indexEdit) {
                    return false;
                }
                return item.value.slug === slug;
            });
        }

        return {
            state,
            schemaCreate,
            schemaEdit,
            closeFormEmailTemplate,
            handleSubmitFormEmailTemplate,
            updateFiles,
            updateFileEdits,
            onAddFiles,
            editEmailTemplate,
            handleEditEmailTemplate,
            removeEmailTemplate,
            closeEditEmailTemplate,
            handleSubmitEditEmailTemplate,
            toggleEmailCc,
            toggleEmailBcc,
            isDropdownVisible,
            isContentDropdownVisible,
            filteredOptionFields,
            filteredContentOptionFields,
            addOptionField,
            handleBlurDropdownHide,
            addOptionFieldEdit,
            addContentOptionField,
            handleBlurContentDropdownHide,
            addContentOptionFieldEdit,
            debouncedGetOptionScopes,
            handleInputNameAdd,
            handleInputNameEdit,
            checkDuplicateSlugAdd,
            checkDuplicateSlugEdit,
            formatApiDataToFormData,
            formatFilesForFilePond,
        }
    },
});
</script>
<style type="text/css" scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color:#f8f9fa;
    padding: 10px;
}
svg, .cursor-pointer {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
.list-group-item {
    cursor: pointer;
}
.list-group-item:hover {
    background-color: #f0f0f0;
    border: none;
}
.dropdown {
    border: none; 
}
ul>li>p {
    margin-bottom: 0px;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>