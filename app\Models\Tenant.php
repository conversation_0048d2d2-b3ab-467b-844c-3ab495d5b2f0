<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Tenant extends Model
{
    use HasUuids;
    protected $table = 'tenants';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'domain_name',
        'start_date',
        'end_date',
        'is_active',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
