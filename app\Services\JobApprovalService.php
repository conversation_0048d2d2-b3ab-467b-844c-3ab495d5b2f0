<?php

namespace App\Services;

use App\Models\Stage;
use App\Models\JobApprovalHistory;
use App\Models\User;
use Carbon\Carbon;
use App\Repositories\User\UserRepositoryInterface;
use App\Repositories\JobApprovalHistory\JobApprovalHistoryRepositoryInterface;
use App\Repositories\SaveJob\SaveJobRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\ProcessInstanceStageStatus;
use App\Enums\ProcessStageStatus;
use App\Enums\SaveJobStatus;

class JobApprovalService
{
    protected $userRepository;
    protected $jobApprovalHistoryRepository;
    protected $saveJobRepository;

    public function __construct(
        UserRepositoryInterface $userRepository,
        JobApprovalHistoryRepositoryInterface $jobApprovalHistoryRepository,
        SaveJobRepositoryInterface $saveJobRepository
    ) {
        $this->userRepository = $userRepository;
        $this->jobApprovalHistoryRepository = $jobApprovalHistoryRepository;
        $this->saveJobRepository = $saveJobRepository;
    }

    /**
     * Lưu lịch sử phê duyệt
     */
    public function saveJobApprovalHistory($fromStageIds, $saveJob, $stage_id, $action_id, $comment, $pending_approval_id)
    {
        // 1. Khi tạo công việc mới nếu $state_id == 'START' thì sẽ tạo 1 bản ghi trong job_approval_history
        // 2. Kiểm tra nếu pending_approval_id có thì cập nhật lại job_approval_history
        // 3. Cập nhật trạng thái trong SaveJob là processing
        // 4. Cập nhật trạng thái trong ProcessInstanceStageStatus là completed
        // 5. Tiếp tục xử lý với $fromStageIds sẽ tạo các bản ghi tiếp theo trong job_approval_history và cập nhật trạng thái status trong ProcessInstanceStageStatus là processing
        // 6. Khi giải đoạn (hành động + giai đoạn đó thực hiện) sẽ cập nhật trạng thái status trong ProcessInstanceStageStatus là completed, và cập nhật lịch sử của bản ghi đó trong job_approval_history là đã hoàn thành
        // 7. Cập nhật trạng thái của bảng save_jobs là processing
        // 8. Nếu $fromStageIds có giá trị là 'done' thì sẽ cập nhật trạng thái của bảng save_jobs là completed
        // 9. Nếu $fromStageIds có giá trị là 'cancel' thì sẽ cập nhật trạng thái của bảng save_jobs là cancel
        try {
            // Kiểm tra dữ liệu đầu vào
            if (empty($fromStageIds) || !$saveJob) {
                Log::warning("Invalid input data for saveJobApprovalHistory", [
                    'fromStageIds' => $fromStageIds,
                    'saveJob' => $saveJob ? $saveJob->id : null,
                    'stage_id' => $stage_id,
                    'action_id' => $action_id
                ]);

                return false;
            }
            
            $job_id = $saveJob->id;
            $processedStages = 0; // Đếm số stage đã xử lý thành công
            
            if (isset($stage_id) && isset($action_id)) {
                $current_user = Auth::user();
                // Tạo bản ghi lịch sử cho giai đoạn 'start' hiện tại
                if ($stage_id === config('constants.stage.START')) {
                    try {
                        $startHistoryData = [
                            'job_id' => $job_id,
                            'stage_id' => $stage_id,
                            'user_id' => $current_user->id,
                            'action_id' => $action_id,
                            'date' => Carbon::now()->toDateTimeString(),
                        ];
                        $this->jobApprovalHistoryRepository->create($startHistoryData);
                    } catch (\Exception $e) {
                        Log::error("Failed to create START stage history", [
                            'job_id' => $job_id,
                            'error' => $e->getMessage()
                        ]);
                        
                        return false;
                    }
                }
                
                // Cập nhật giai lịch sự phê duyệt trong bảng job_approval_history
                if (isset($pending_approval_id)) {
                    try {
                        $job_approval_history = $this->jobApprovalHistoryRepository->find($pending_approval_id);
                        if ($job_approval_history) {
                            $dataHistory = [
                                'user_id' => $current_user->id,
                                'action_id' => $action_id,
                                'date' => Carbon::now()->toDateTimeString(),
                                'comment' => $comment,
                            ];
                            
                            $this->jobApprovalHistoryRepository->update($dataHistory, $job_approval_history->id);

                            // Cập nhật trạng thái của bảng save_jobs là processing
                            if ($saveJob) {
                                $saveJob->status = SaveJobStatus::PROCESSING->value;
                                $saveJob->save();
                            }

                            // Cập nhật trạng thái giai đoạn trong bảng process_instance_stage_status là completed
                            $processInstanceStageStatus = $saveJob->processInstanceStageStatus()->where('stage_id', $stage_id)->first();
                            if ($processInstanceStageStatus) {
                                $processInstanceStageStatus->status = ProcessStageStatus::COMPLETED->value;
                                $processInstanceStageStatus->save();
                            }
                        } else {
                            Log::warning("Pending approval history not found", [
                                'pending_approval_id' => $pending_approval_id
                            ]);
                        }
                    } catch (\Exception $e) {
                        Log::error("Failed to update pending approval", [
                            'job_id' => $job_id,
                            'pending_approval_id' => $pending_approval_id,
                            'error' => $e->getMessage()
                        ]);

                        return false;
                    }
                }
            }

            $jobCompleted = false; // Cờ để đánh dấu nếu gặp giai đoạn kết thúc
            $jobCancelled = false; // Cờ để đánh dấu nếu gặp giai đoạn hủy

            // --- 2. Xử lý các giai đoạn tiếp theo ($nextStageIds) ---
            foreach ($fromStageIds as $stageId) {
                try {
                    // Kiểm tra nếu giai đoạn là 'cancel'
                    if ($stageId === config('constants.stage.FALSE')) {
                        $jobCancelled = true;
                        $processedStages++;
                        continue; // Bỏ qua, sẽ xử lý ở cuối hàm
                    }

                    // Kiểm tra xem $stageId hiện tại có phải là ID của giai đoạn 'done' không
                    if ($stageId === config('constants.stage.DONE')) {
                        $jobCompleted = true;
                        $processedStages++;
                        // Không tạo bản ghi history 'pending' cho giai đoạn done, chuyển sang stage tiếp theo
                        continue; // Bỏ qua việc tạo history và lấy thông tin stage cho giai đoạn done
                    }

                    // Lấy thông tin Stage từ DB
                    $stage = Stage::find($stageId);

                    if (!$stage) {
                        Log::warning("Stage not found", ['stage_id' => $stageId]);
                        continue; // Bỏ qua stage ID không hợp lệ
                    }

                    // Lấy approved_list và followers từ Stage
                    $approvedList = $this->userRepository->getUserByOptionScopeRes($stage->approver ?? []);
                    $followers = $this->userRepository->getUserByOptionScopeRes($stage->followers ?? []);
                    
                    // Chuẩn bị dữ liệu cho bản ghi chờ duyệt
                    $pendingHistoryData = [
                        'job_id'        => $job_id,
                        'stage_id'      => $stageId,
                        'approved_list' => !empty($approvedList) ? array_column($approvedList, 'value') : null,
                        'followers'     => !empty($followers) ? array_column($followers, 'value') : null,
                    ];
                    
                    // Tạo bản ghi mới
                    $historyRecord = $this->jobApprovalHistoryRepository->create($pendingHistoryData);
                    
                    if (!$historyRecord) {
                        Log::error("Failed to create job approval history", [
                            'job_id' => $job_id,
                            'stage_id' => $stageId
                        ]);
                        continue;
                    }

                    // Tạo trạng thái processing trong bảng process_instance_stage_status
                    $existingStageStatus = $saveJob->processInstanceStageStatus()->where('stage_id', $stageId)->exists();

                    if (!$existingStageStatus) {
                        $data_process_instance_stage_status = [
                            'job_id' => $job_id,
                            'stage_id' => $stageId,
                            'status' => ProcessStageStatus::PROCESSING->value,
                        ];
    
                        $stageStatus = ProcessInstanceStageStatus::create($data_process_instance_stage_status);
                        
                        if (!$stageStatus) {
                            Log::error("Failed to create process instance stage status", [
                                'job_id' => $job_id,
                                'stage_id' => $stageId
                            ]);
    
                            continue;
                        }
                    }
                    
                    $processedStages++;
                    
                } catch (\Exception $e) {
                    Log::error("Error processing stage", [
                        'job_id' => $job_id,
                        'stage_id' => $stageId,
                        'error' => $e->getMessage()
                    ]);
                    // Tiếp tục xử lý stage khác thay vì dừng lại
                    continue;
                }
            }
            
            // --- 3. Cập nhật trạng thái cuối cùng của Job ---
            if ($saveJob) {
                try {
                    if ($jobCancelled) {
                        $saveJob->status = SaveJobStatus::CANCEL->value;
                        $saveJob->save();
                        $saveJob->processInstanceStageStatus()->update(['status' => ProcessStageStatus::CANCEL->value]);
                    } else if ($jobCompleted) {
                        $saveJob->status = SaveJobStatus::COMPLETED->value;
                        $saveJob->save();
                        $saveJob->processInstanceStageStatus()->update(['status' => ProcessStageStatus::COMPLETED->value]);
                    }
                } catch (\Exception $e) {
                    Log::error("Failed to update job final status", [
                        'job_id' => $job_id,
                        'error' => $e->getMessage()
                    ]);

                    return false;
                }
            }
            
            // Kiểm tra xem có ít nhất một stage được xử lý thành công không
            if ($processedStages === 0) {
                Log::warning("No stages were processed successfully", [
                    'job_id' => $job_id,
                    'fromStageIds' => $fromStageIds
                ]);

                return false;
            }
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("saveJobApprovalHistory failed", [
                'job_id' => $saveJob ? $saveJob->id : null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }
} 