import { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import Cookies from 'js-cookie';

/**
 * Authentication guard for routes
 * - Redirects to login if not authenticated
 * - Checks for permission requirements
 */
export async function authGuard(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
): Promise<void> {
    const authStore = useAuthStore();
    const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
    const requiredPermissions = Array.from(
        new Set(
            to.matched
            .flatMap(r => (r.meta.permissions as string[] | undefined) ?? []),
        ),
    );
    const isLoginPage = to.path === '/login';

    // IMPORTANT: This is the only place where initAuth() should be called
    // to prevent duplicate API calls to /api/auth/user on page refresh
    if (!authStore.isAuthenticated) {
        try {
            await authStore.initAuth();
        } catch (error) {
            console.error('Error initializing auth:', error);
        }
    }

    // If already authenticated and trying to access login page, redirect to home
    if (authStore.isAuthenticated && isLoginPage) {
        return next({ path: '/home' });
    }

    // If route requires auth and user is not authenticated
    if (requiresAuth && !authStore.isAuthenticated) {
        // Save the intended destination for redirect after login
        Cookies.set('redirect_path', to.fullPath, { expires: 1, sameSite: 'strict' });
        return next({ path: '/login' });
    }

    // Check permissions if specified in route meta
    if (authStore.isAuthenticated && requiredPermissions && requiredPermissions.length > 0) {
        // Check for permissions update from server
        await authStore.checkPermissionsUpdate();
        
        // Verify user has required permissions
        const hasPermission = authStore.hasAnyPermission(requiredPermissions);
        
        if (!hasPermission) {
            return next({ path: '/home' });
        }
    }

    // Proceed with navigation
    return next();
}
