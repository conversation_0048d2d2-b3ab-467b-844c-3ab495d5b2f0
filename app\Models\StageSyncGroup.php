<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class StageSyncGroup extends Model
{
    use HasUuids;
    
    protected $table = 'stage_sync_groups';
    protected $primaryKey = 'id';

    protected $fillable = [
        'stage_id',
        'sync_group_id',
        'action_id',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    /**
     * <PERSON><PERSON><PERSON> quan hệ với bảng sync_groups
     */
    public function syncGroup()
    {
        return $this->belongsTo('App\Models\SyncGroup', 'sync_group_id', 'id');
    }

    /**
     * <PERSON><PERSON><PERSON> quan hệ với bảng actions
     */
    public function action()
    {
        return $this->belongsTo('App\Models\Action', 'action_id', 'id');
    }
}
