<template>
	<CHeader position="sticky" :class="headerClassNames">
		<CContainer class="border-bottom px-4" fluid>
			<CHeaderToggler @click="sidebar.toggleVisible()" style="margin-inline-start: -14px">
				<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#83868C"><path d="M120-240v-80h720v80H120Zm0-200v-80h720v80H120Zm0-200v-80h720v80H120Z"/></svg>
			</CHeaderToggler>
			<CHeaderNav class="d-none d-md-flex">
			</CHeaderNav>
			<CHeaderNav class="ms-auto">
				<CNavItem>
					<CNavLink href="#">
						<CIcon icon="cil-bell" size="lg" />
					</CNavLink>
				</CNavItem>
				<CNavItem>
					<CNavLink href="#">
						<CIcon icon="cil-list" size="lg" />
					</CNavLink>
				</CNavItem>
				<CNavItem>
					<CNavLink href="#">
						<CIcon icon="cil-envelope-open" size="lg" />
					</CNavLink>
				</CNavItem>
			</CHeaderNav>
			<CHeaderNav>
				<li class="nav-item py-1">
					<div class="vr h-100 mx-2 text-body text-opacity-75"></div>
				</li>
				<CDropdown variant="nav-item" placement="bottom-end">
				<CDropdownToggle :caret="false">
					<CIcon v-if="colorMode === 'dark'" icon="cil-moon" size="lg" />
					<CIcon v-else-if="colorMode === 'light'" icon="cil-sun" size="lg" />
					<CIcon v-else icon="cil-contrast" size="lg" />
				</CDropdownToggle>
					<CDropdownMenu>
						<CDropdownItem
							:active="colorMode === 'light'"
							class="d-flex align-items-center"
							component="button"
							type="button"
							@click="setColorMode('light')"
						>
						<CIcon class="me-2" icon="cil-sun" size="lg" /> Light
						</CDropdownItem>
						<CDropdownItem
							:active="colorMode === 'dark'"
							class="d-flex align-items-center"
							component="button"
							type="button"
							@click="setColorMode('dark')"
						>
						<CIcon class="me-2" icon="cil-moon" size="lg" /> Dark
						</CDropdownItem>
						<CDropdownItem
							:active="colorMode === 'auto'"
							class="d-flex align-items-center"
							component="button"
							type="button"
							@click="setColorMode('auto')"
						>
						<CIcon class="me-2" icon="cil-contrast" size="lg" /> Auto
						</CDropdownItem>
					</CDropdownMenu>
				</CDropdown>
				<li class="nav-item py-1">
					<div class="vr h-100 mx-2 text-body text-opacity-75"></div>
				</li>
				<AppHeaderDropdownAccnt />
			</CHeaderNav>
		</CContainer>
		<CContainer class="px-4" fluid>
			<AppBreadcrumb />
		</CContainer>
	</CHeader>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { useColorModes } from '@coreui/vue';
import AppHeaderDropdownAccnt from '@/components/AppHeaderDropdownAccnt.vue';
import { useSidebarStore } from '@/stores/sidebar';
import AppBreadcrumb from '@/components/AppBreadcrumb.vue'

export default defineComponent({
	components: {
		AppHeaderDropdownAccnt,
		AppBreadcrumb
	},
	setup() {
		const headerClassNames = ref('mb-4 p-0');
		const { colorMode, setColorMode } = useColorModes('tvnas-template-theme');
		const sidebar: any = useSidebarStore();

		onMounted(() => {
			document.addEventListener('scroll', () => {
				if (document.documentElement.scrollTop > 0) {
					headerClassNames.value = 'mb-4 p-0 shadow-sm';
				} else {
					headerClassNames.value = 'mb-4 p-0';
				}
			});
		});

		return {
			headerClassNames,
			colorMode,
			setColorMode,
			sidebar,
		};
	},
});
</script>

