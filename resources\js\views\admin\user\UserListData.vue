<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-1">
				<CCardHeader>
					<div class="d-flex justify-content-start align-items-center">
						<b-dropdown size="lg" variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab">
							<template #button-content>
								<span class="material-icons-outlined">tune</span>
							</template>
							<b-dropdown-item 
								v-for="(tab, index) in optionTabs" 
								:key="index" 
								@click="handleClickTab(tab.value)"
							>
								{{ tab.title }} ({{ dataCounts[tab.value] }})
							</b-dropdown-item>
							<b-dropdown-divider></b-dropdown-divider>
							<b-dropdown-item>
                                <div class="d-inline-flex align-items-center">
                                    <span class="material-symbols-outlined">auto_fix_high</span>
                                    <span class="m-2">{{ $t('menu_tab.custom') }}</span>
                                </div>
                            </b-dropdown-item>
						</b-dropdown>
						<ul class="nav">
							<li v-for="(tab, index) in optionTabs" :key="index" class="nav-item">
								<a 
									:class="{ active: isActiveTab(tab.value) }" 
									class="nav-link"
									@click="handleClickTab(tab.value)"
								>
									{{ tab.title }} ({{ dataCounts[tab.value] }})
								</a>
							</li>
						</ul>
						<div class="ms-auto">
							<router-link
                                :to="{ name: 'UserAdd' }"
                                class="btn btn-light d-flex align-items-center"
                            >
                                <span class="material-symbols-outlined me-1">add_circle</span>
								<span class="fw-normal">{{ $t('user.add') }} </span>
                            </router-link>
						</div>
					</div>
				</CCardHeader>
			</CCard>
			<CCard class="mb-4">
				<CCardHeader>
					<paginate 
						:meta="paginate.meta" 
						:links="paginate.links" 
						@page="page" 
						@per-page="perPage"
					>
					</paginate>
				</CCardHeader>
				<CCardBody>
					<user-table
						:dataUsers="dataUsers"
						@refresh-data="refreshCurrentPage"
					>
					</user-table>
				</CCardBody>
			</CCard>
		</CCol>
	</CRow>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router';
import { useI18n } from "vue-i18n";
import UserTable from '@/views/admin/user/UserTable.vue';
import Paginate from '@/views/paginate/Paginate.vue';
import useUsers from '@/composables/user';
import { USER_STATUS } from "@/constants/constants";
  
export default defineComponent({
    name: "UserListData",

	components: {
		UserTable,
		Paginate
	},
  
    setup() {
		const route = useRoute();
        const { t }  = useI18n();

		const state = reactive({
			tabActived: '' as any,
			paginate: {
				page: 1,
		        perPage: 10,
			},
		});
		
		// Sử dụng computed để optionTabs cập nhật khi ngôn ngữ thay đổi
		const optionTabs = computed(() => [
			{ value: USER_STATUS.ALL, title: t('option_tab_user.all') },
			{ value: USER_STATUS.ACTIVE, title: t('option_tab_user.active') },
			{ value: USER_STATUS.UNACTIVE, title: t('option_tab_user.unactive') },
		]);

		const isActiveTab = (valueTabActived: string) => {
			return route.query.tab === valueTabActived;
		};

		const handleClickTab = (valueTabActived: string) => {
			state.tabActived = valueTabActived;
			getAllUsers(
				state.paginate.page, 
				state.paginate.perPage, 
				state.tabActived
			);
		};

		const { setIsLoading, paginate, dataUsers, dataCounts, getAllUsers } = useUsers();

		const refreshData = (): void => {
			state.tabActived = route.query.tab || USER_STATUS.ALL;
			getAllUsers(
				state.paginate.page,
				state.paginate.perPage,
				state.tabActived
			);
        };

		// Refresh data while keeping current page
		const refreshCurrentPage = (): void => {
			state.tabActived = route.query.tab || USER_STATUS.ALL;
			// Use current page from paginate meta, fallback to state, then to 1
			const currentPage = Number(paginate.meta.currentPage) || state.paginate.page || 1;
			const currentPerPage = Number(paginate.meta.perPage) || state.paginate.perPage || 10;
			// Update state to match current pagination
			state.paginate.page = currentPage;
			state.paginate.perPage = currentPerPage;
			getAllUsers(
				currentPage,
				currentPerPage,
				state.tabActived
			);
		};
 
        onMounted(() => {
		 	refreshData();
		})

        const page = (page: number): void => {
            getAllUsers(
				page, 
				state.paginate.perPage,
				state.tabActived
			);
        };

        const perPage = (perPage: number): void => {
            state.paginate.perPage = perPage;
            getAllUsers(
				state.paginate.page, 
				perPage,
				state.tabActived
			);
        };

		return {
			state,
			optionTabs,
			setIsLoading,
			paginate,
			isActiveTab,
			handleClickTab,
			dataUsers,
			dataCounts,
			refreshCurrentPage,
			page,
			perPage,
		}
    }
});
</script>

<style scoped>

</style>