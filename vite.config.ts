import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import autoprefixer from 'autoprefixer';
import Components from 'unplugin-vue-components/vite'
import {BootstrapVueNextResolver} from 'bootstrap-vue-next'

export default defineConfig(() => {
	return {
		plugins: [
			laravel({
				input: [
					'resources/css/app.css',
					'resources/js/app.ts',
				],
				refresh: true,
			}),
			vue({
				template: {
					transformAssetUrls: {
						base: null,
						includeAbsolute: false,
					},
				},
			}),
			Components({
				resolvers: [BootstrapVueNextResolver()],
			}),
		],
		css: {
			postcss: {
				plugins: [
					autoprefixer({}), // add options if needed
				],
			},
			preprocessorOptions: {
				scss: {
					quietDeps: true,
				},
			},
		},
		resolve: {
			alias: {
				vue: 'vue/dist/vue.esm-bundler.js',
				'@': path.resolve(__dirname, 'resources/js')
			},
			extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue', '.scss', '.png'],
		},
	};
});
