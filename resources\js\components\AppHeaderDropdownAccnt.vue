<template>
    <CDropdown placement="bottom-end" variant="nav-item">
        <CDropdownToggle class="py-0 pe-0" :caret="false">
            <CAvatar :color="avatarColor" size="md">{{ userInitial }}</CAvatar>
        </CDropdownToggle>
        <CDropdownMenu class="pt-0">
            <CDropdownItem>
                <CBadge color="secondary" class="ms-auto"></CBadge>
            </CDropdownItem>
            <CDropdownItem> 
                <CAvatar :color="avatarColor" size="md">{{ userInitial }}</CAvatar>
                <span class="m-3">{{ authStore.user?.full_name || 'User' }}</span>
            </CDropdownItem>
            <CDropdownDivider />
            <CDropdownItem> 
                <div class="d-inline-flex align-items-center">
                    <span class="material-symbols-outlined">person</span>
                    <span class="m-2">{{ $t('dropdown_acc.account') }}</span>
                </div>
            </CDropdownItem>
            <CDropdownItem> 
                <div class="d-inline-flex align-items-center">
                    <span class="material-symbols-outlined">book</span>
                    <span class="m-2">{{ $t('dropdown_acc.book') }}</span>
                </div>
            </CDropdownItem>
            <CDropdownItem> 
                <div class="d-inline-flex align-items-center">
                    <span class="material-symbols-outlined">palette</span>
                    <span class="m-2">{{ $t('dropdown_acc.change_color') }}</span>
                </div>
            </CDropdownItem>
            <CDropdownItem class="language-dropdown-wrapper">
                <div class="d-inline-flex align-items-center">
                    <span class="material-symbols-outlined">translate</span>
                    <span class="m-2">{{ $t('dropdown_acc.change_lang') }}</span>
                    <span class="current-language">
                        <span>{{ currentLanguageName }}</span>
                    </span>
                </div>
                <div class="language-options language-options-left">
                    <div class="language-option" @click="changeLanguage('en')" :class="{'active': authStore.language === 'en'}">
                        <span>{{ $t('auth.lang.en') }}</span>
                    </div>
                    <div class="language-option" @click="changeLanguage('vi')" :class="{'active': authStore.language === 'vi'}">
                        <span>{{ $t('auth.lang.vi') }}</span>
                    </div>
                </div>
            </CDropdownItem>
            <CDropdownDivider />
            <CDropdownItem @click="changePassword"> 
                <div class="d-inline-flex align-items-center">
                    <span class="material-symbols-outlined">vpn_key</span>
                    <span class="m-2">{{ $t('dropdown_acc.change_pass') }}</span>
                </div>
            </CDropdownItem>
            <CDropdownItem @click="handleLogout"> 
                <div class="d-inline-flex align-items-center">
                    <span class="material-symbols-outlined">logout</span>
                    <span class="m-2">{{ $t('dropdown_acc.logout') }}</span>
                </div>
            </CDropdownItem>
        </CDropdownMenu>
    </CDropdown>
    <BModal 
        centered 
        scrollable
        no-close-on-backdrop
        no-close-on-esc
        :title="$t('account.change_password')"
        v-model="state.modalChangePassword"
    >
		<div v-if="state.showComponentModal">
			<change-password
				@close-modal="hideModal"
			>
			</change-password>
		</div>
    </BModal>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, computed, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import ChangePassword from '@/components/account/ChangePassword.vue'
import { changeAppLanguage } from '@/utils/languageUtils'

export default defineComponent({
	name: 'AppHeaderDropdownAccnt',

	components: {
		ChangePassword,
	},
	
	setup() {
		const authStore = useAuthStore();

		// Available colors for avatar background
		const colors = [
			'primary', 'secondary', 'success', 'danger', 
			'warning', 'info', 'light'
		];

		// Generate random color for avatar
		const avatarColor = computed(() => {
			const randomIndex = Math.floor(Math.random() * colors.length);
			return colors[randomIndex];
		});

		// Get user's first letter for avatar
		const userInitial = computed(() => {
			if (authStore.user?.full_name) {
				return authStore.user.full_name.charAt(0).toUpperCase();
			}
			return 'U';
		});

		const state = reactive({
			showComponentModal: false,
			modalChangePassword: false,
		});

		onMounted(async () => {
			// Fetch current user if not already authenticated
			if (!authStore.isAuthenticated) {
				await authStore.fetchCurrentUser();
			}
		});

		const account = (): void => {
			state.showComponentModal  = true;
		}

		const changePassword = (): void => {
			state.showComponentModal  = true;
			state.modalChangePassword = true;
		}

		const handleLogout = async (): Promise<void> => {
			try {
				await authStore.logout();
			} catch (error) {
				console.error('Error during logout:', error);
			}
		}

		const hideModal = () => {
            state.modalChangePassword = false;
        };
        
        const currentLanguageName = computed(() => {
            return authStore.language === 'en' ? 'EN' : 'VI';
        });

        const changeLanguage = (lang: string) => {
            // Sử dụng hàm changeAppLanguage để thay đổi ngôn ngữ một cách nhất quán
            changeAppLanguage(lang);
            
            // Cập nhật ngôn ngữ trong auth store
            authStore.language = lang;
            
            // Cập nhật trong user object (nếu có)
            if (authStore.user) {
                authStore.user.language = lang;
            }
            
            // Gọi API cập nhật ngôn ngữ cho tài khoản nếu đã đăng nhập
            if (authStore.isAuthenticated && authStore.user) {
                // Gửi request cập nhật ngôn ngữ lên server (có thể thêm sau nếu cần)
                // axios.post('/api/user/update-language', { language: lang });
            }
        };

		return {
			state,
			authStore,
			avatarColor,
			userInitial,
			account,
			changePassword,
			hideModal,
			handleLogout,
			changeLanguage,
            currentLanguageName
		}
  	}
});
</script>

<style scoped>
.language-dropdown-wrapper {
    position: relative;
}

.language-dropdown-wrapper:hover .language-options {
    display: block;
}

.language-options {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    min-width: 150px;
    z-index: 1000;
}

.language-options-left {
    left: -150px;
    right: auto;
}

.current-language {
    margin-left: 10px;
    display: inline-flex;
    align-items: center;
    font-size: 0.9em;
    color: #666;
    background-color: #f8f9fa;
    padding: 2px 8px;
    border-radius: 4px;
}

.language-option {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.language-option:hover, .language-option.active {
    background-color: #f8f9fa;
}

.language-option.active {
    font-weight: bold;
    color: var(--cui-primary, #321fdb);
}

.language-flag {
    margin-right: 8px;
    font-size: 16px;
}
</style>


