<template>
  <!-- Auth middleware for permission checking -->
  <auth-middleware />
  
  <Teleport to="#target"></Teleport>
      <div v-if="error">
          {{ error }}
      </div>
  <router-view v-slot="{ Component }" v-else>
      <template v-if="Component">
          <suspense timeout="0">
              <template #default>
                  <component :is="Component"></component>
              </template>
              <template #fallback>
                  <!-- <div>Loading... Please wait.</div> -->
                  <loading :isLoading="true" />
              </template>
          </suspense>
      </template>
  </router-view>
</template>

<script lang="ts">
import { ref, onErrorCaptured, defineComponent, onBeforeMount } from 'vue'
import { useColorModes } from '@coreui/vue'
import { useThemeStore } from '@/stores/theme'
import AuthMiddleware from '@/components/auth/AuthMiddleware.vue'
import Loading from '@/views/loading/Loading.vue'


export default defineComponent({
    name: 'App',
    
    components: {
		AuthMiddleware,
		Loading
	},

    setup () {
        const error = ref(null);

        onErrorCaptured((e: any) => {
            error.value = e;

            return true;
        });

		const { isColorModeSet, setColorMode } = useColorModes(
			'tvnas-template-theme',
		);
		const currentTheme = useThemeStore();

		onBeforeMount(() => {
			const urlParams = new URLSearchParams(window.location.href.split('?')[1]);
			let theme: any = urlParams.get('theme');

			if (theme !== null && theme.match(/^[A-Za-z0-9\s]+/)) {
				theme = theme.match(/^[A-Za-z0-9\s]+/)[0];
			}

			if (theme) {
				setColorMode(theme);
				return;
			}

			if (isColorModeSet()) {
				return;
			}

			setColorMode(currentTheme.theme);
		});

        return { error };
    }
});


</script>

<style lang="scss">
// Import Main styles for this application
@use 'styles/style';
// We use those styles to show code examples, you should remove them in your application.
@use 'styles/examples';
</style>
