<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\ActiveGlobalScopeTrait;

class PermissionGroup extends Model
{
    use ActiveGlobalScopeTrait, HasFactory, HasUuids;

    protected $table = 'permission_groups';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'is_active',
        'create_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function permissions()
    {
        return $this->hasMany(Permission::class, 'permission_group_id', 'id');
    }
}
