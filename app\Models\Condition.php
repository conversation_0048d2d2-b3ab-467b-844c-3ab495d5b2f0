<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Condition extends Model
{
    use HasUuids;
    protected $table = 'conditions';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'slug',
        'or_conditions',
        'process_version_id',
    ];

    protected $casts = [
        'or_conditions' => 'array', 
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
