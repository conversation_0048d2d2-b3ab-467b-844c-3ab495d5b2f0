<template>
	<div>
		<div class="mb-3" v-for="(fieldItem, index) in formFields" :key="index">
			<CCol :xs="fieldItem.column_width">
				<div v-if="fieldItem.type === 'MULTISELECT'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						v-slot="{ field }"
					>
						<Multiselect
							v-bind="field"
							:mode="fieldItem.multiple ? 'tags' : 'single'"
							:value="fieldItem.value"
							:model-value="formData[fieldItem.name]"
							@update:model-value="(value) => $emit('update-form-data', fieldItem.name, value)"
							:placeholder="fieldItem.placeholder"
							:close-on-select="false"
							:searchable="true"
							:disabled="fieldItem.disabled"
							:options="fieldItem.options"
							:can-clear="fieldItem.validation ? false : true"
						/>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'USER'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						v-slot="{ field }"
					>
						<Multiselect
							v-bind="field"
							:mode="fieldItem.multiple ? 'tags' : 'single'"
							:value="fieldItem.value"
							:model-value="formData[fieldItem.name]"
							@update:model-value="(value) => $emit('update-form-data', fieldItem.name, value)"
							:placeholder="fieldItem.placeholder"
							:close-on-select="false"
							:filter-results="false"
							:resolve-on-load="false"
							:infinite="true"
							:limit="10"
							:clear-on-search="true"
							:searchable="true"
							:delay="0"
							:min-chars="0"
							:object="true"
							:disabled="fieldItem.disabled"
							:options="async (query) => {
								return await getOptionUsers(query)
							}"
							:can-clear="fieldItem.validation ? false : true"
							@open="getOptionUsers('')"
						/>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'DEPARTMENT'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						v-slot="{ field }"
					>
						<Multiselect
							v-bind="field"
							:mode="fieldItem.multiple ? 'tags' : 'single'"
							:value="fieldItem.value"
							:model-value="formData[fieldItem.name]"
							@update:model-value="(value) => $emit('update-form-data', fieldItem.name, value)"
							:placeholder="fieldItem.placeholder"
							:close-on-select="false"
							:searchable="true"
							:object="true"
							:disabled="fieldItem.disabled"
							:options="selectOptionDepartments"
							:can-clear="fieldItem.validation ? false : true"
						>
							<template v-slot:option="{ option }">
								<div class="custom-option">
									<div class="option-label mb-1">
										{{ option.label }}
									</div>
									<div class="option-description text-secondary">
										<small>
											<i>{{ option.type }}</i>
										</small>
									</div>
								</div>
							</template>
						</Multiselect>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'FILEUPLOAD'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						:model-value="formData[fieldItem.name]"
					>
						<FilePond
							:files="formData[fieldItem.name]"
							@updatefiles="(fileItemUploads) => $emit('update-files', fileItemUploads, fieldItem.name)"
							className="file-pond"
							:labelIdle="$t('validate_field.file_upload.label_idle')"
							:allowMultiple="fieldItem.multiple"
							:maxFiles="maxFiles"
							:maxFileSize="maxFileSize"
							:acceptedFileTypes="acceptedFileTypes"
							:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
							:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
							:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
							:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
							:instantUpload="false"
							:name="fieldItem.name"
							:ref="fieldItem.name"
							credits="false"
							allow-reorder="true"
							item-insert-location="after"
							image-preview-min-height="60"
							image-preview-max-height="60"
						/>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'FORMULA'">
					<FormKit
						type="text"
						:label="fieldItem.label"
						:floating-label="fieldItem.floatingLabel"
						:name="fieldItem.name"
						:model-value="formattedFormulaResults[fieldItem.name]"
						disabled="false"
						label-class="required-label"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'TABLE'">
					<DynamicTable
						:field-item="fieldItem"
						:item-childrens="itemChildrens"
						:select-option-departments="selectOptionDepartments"
						:sub-column-table-description-children="subColumnTableDescriptionChildren"
						:sub-column-table-option-selected-children="subColumnTableOptionSelectedChildren"
						:max-files="maxFiles"
						:max-file-size="maxFileSize"
						:accepted-file-types="acceptedFileTypes"
						:get-option-users="getOptionUsers"
						:get-option-column-data="getOptionColumnData"
						:form-data="formData"
						@update-file-childrens="(fileItemUploads, itemChildren, fieldChildrenName) => $emit('update-file-childrens', fileItemUploads, itemChildren, fieldChildrenName)"
						@add-item="(field) => $emit('add-item', field)"
						@remove-item="(field, itemIndex) => $emit('remove-item', field, itemIndex)"
						@show-sub-column-table-children="(optionSelected, itemIndex, keyName) => $emit('show-sub-column-table-children', optionSelected, itemIndex, keyName)"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'OBJECTSYSTEM'">
					<div class="d-flex gap-2">
						<div :class="[`col`, `column-width-td`]">
							<label class="mb-1">
								{{ fieldItem.label }}
								<span v-if="fieldItem.validation" class="text-danger">*</span>
							</label>
							<Field 
								:name="fieldItem.name"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="fieldItem.multiple ? 'tags' : 'single'"
									:value="fieldItem.value"
									:model-value="formData[fieldItem.name]"
									@update:model-value="(value) => $emit('update-form-data', fieldItem.name, value)"
									:placeholder="fieldItem.placeholder"
									:close-on-select="false"
									:filter-results="false"
									:resolve-on-load="false"
									:infinite="true"
									:limit="10"
									:clear-on-search="true"
									:searchable="true"
									:delay="0"
									:min-chars="0"
									:object="true"
									:disabled="fieldItem.disabled"
									:options="async (query) => {
										return await getOptionColumnData(query, fieldItem)
									}"
									:can-clear="fieldItem.validation ? false : true"
									@change="$emit('show-sub-column-table', $event, fieldItem.name)"
									@open="getOptionColumnData('', fieldItem)"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="fieldItem.name"
								class="text-danger"
							/>
						</div>
						<div :class="[`col`, `column-width-td`]" class="disabled-column-table-description" v-for="(subColumnTableDescription, keyNameSubColumnTable) in subColumnTableDescription[fieldItem.name]" :key="keyNameSubColumnTable">
							<label></label>
							<FormKit
								type="text"
								:label="subColumnTableDescription"
								:floating-label="true"
								:model-value="subColumnTableOptionSelected[fieldItem.name] ? subColumnTableOptionSelected[fieldItem.name][keyNameSubColumnTable] : ''"
								disabled
							/>
						</div>
					</div>
				</div>
				<div v-else>
					<FormKit
						v-bind="fieldItem"
						:model-value="formData[fieldItem.name]"
						@update:model-value="(value) => $emit('update-form-data', fieldItem.name, value)"
					/>
				</div>
			</CCol>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { Field, ErrorMessage } from 'vee-validate'
import Multiselect from '@vueform/multiselect'
import vueFilePond from 'vue-filepond'
import 'filepond/dist/filepond.min.css'
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css'
import FilePondPluginImagePreview from 'filepond-plugin-image-preview'
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type'
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size'
import DynamicTable from '@/components/DynamicTable.vue'
import useDynamicFormLogic from '@/composables/useDynamicFormLogic'

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize)

export default defineComponent({
	name: 'DynamicFormFields',
	components: {
		Field,
		ErrorMessage,
		Multiselect,
		FilePond,
		DynamicTable
	},
	props: {
		formFields: {
			type: Array as () => any[],
			required: true
		},
		formData: {
			type: Object as () => any,
			required: true
		},
		itemChildrens: {
			type: Object as () => any,
			required: true
		},
		selectOptionDepartments: {
			type: Array as () => any[],
			required: true
		},
		subColumnTableDescription: {
			type: Object as () => any,
			required: true
		},
		subColumnTableOptionSelected: {
			type: Object as () => any,
			required: true
		},
		subColumnTableDescriptionChildren: {
			type: Object as () => any,
			required: true
		},
		subColumnTableOptionSelectedChildren: {
			type: Object as () => any,
			required: true
		},
		formattedFormulaResults: {
			type: Object as () => any,
			required: true
		},
		maxFiles: {
			type: Number,
			required: true
		},
		maxFileSize: {
			type: String,
			required: true
		},
		acceptedFileTypes: {
			type: Array as () => string[],
			required: true
		},
		getOptionUsers: {
			type: Function,
			required: true
		},
		getOptionColumnData: {
			type: Function,
			required: true
		}
	},
	emits: [
		'update-form-data',
		'update-files',
		'update-file-childrens',
		'add-item',
		'remove-item',
		'show-sub-column-table',
		'show-sub-column-table-children'
	],
	setup(props, { emit }) {
		const {
			converFormFields,
			addItem,
			removeItem,
			showSubColumnTable,
			showSubColumnTableChildren,
			updateFiles,
			updateFileChildrens
		} = useDynamicFormLogic()

		// Wrapper functions để emit events
		const handleAddItem = (field: any) => {
			addItem(field, props.itemChildrens);
			emit('add-item', field);
		};

		const handleRemoveItem = (field: any, itemIndex: number) => {
			removeItem(field, itemIndex, props.itemChildrens);
			emit('remove-item', field, itemIndex);
		};

		const handleShowSubColumnTable = (optionSelected: any, keyName: string) => {
			showSubColumnTable(optionSelected, keyName, props.subColumnTableDescription, props.subColumnTableOptionSelected);
			emit('show-sub-column-table', optionSelected, keyName);
		};

		const handleShowSubColumnTableChildren = (optionSelected: any, itemIndex: number, keyName: string) => {
			showSubColumnTableChildren(
				optionSelected,
				itemIndex,
				keyName,
				props.subColumnTableDescriptionChildren,
				props.subColumnTableOptionSelectedChildren
			);
			emit('show-sub-column-table-children', optionSelected, itemIndex, keyName);
		};

		const handleUpdateFiles = (fileItemUploads: any, fieldName: string) => {
			updateFiles(fileItemUploads, fieldName, props.formData);
			emit('update-files', fileItemUploads, fieldName);
		};

		const handleUpdateFileChildrens = (fileItemUploads: any, itemChildren: any, fieldChildrenName: string) => {
			updateFileChildrens(fileItemUploads, itemChildren, fieldChildrenName);
			emit('update-file-childrens', fileItemUploads, itemChildren, fieldChildrenName);
		};

		return {
			converFormFields,
			handleAddItem,
			handleRemoveItem,
			handleShowSubColumnTable,
			handleShowSubColumnTableChildren,
			handleUpdateFiles,
			handleUpdateFileChildrens
		}
	}
})
</script>

<style scoped>
.column-width-td {
	min-width: 200px !important;
}
.disabled-column-table-description {
	pointer-events: none;
}
</style>
