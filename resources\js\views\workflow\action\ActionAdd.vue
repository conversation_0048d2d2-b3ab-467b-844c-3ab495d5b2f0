<template>
    <CCol :xs="12">
        <BTabs no-body content-class="mt-3" v-model="state.tabIndex">
            <BTab :title="$t('workflow.action.create')">
                <FormKit
                    ref="formAction"
                    type="form"
                    :actions="false"
                    incomplete-message=" "
                    @submit="handleSubmitFormAction"
                >
                    <FormKit
                        label-class="required-label" 
                        type="text"
                        :label="$t('workflow.action.name')"
                        :floating-label="false"
                        v-model="dataAction.name"
                        validation="required|length:1,100"
                        @input="(value) => handleInputNameAdd(value)"
                        :validation-messages="{
                            required: `${$t('workflow.action.name')} ${$t('workflow.action.validate.required')}`,
                            length: `${$t('workflow.action.name')} ${$t('workflow.action.validate.name_length')}`
                        }"
                    />
                    <FormKit
                        label-class="required-label" 
                        type="text"
                        :label="$t('workflow.action.slug')"
                        :floating-label="false"
                        v-model="dataAction.slug"
                        validation="required|length:1,100"
                        :readonly="true"
                        :validation-messages="{
                            required: `${$t('workflow.action.slug')} ${$t('workflow.action.validate.required')}`,
                            length: `${$t('workflow.action.slug')} ${$t('workflow.action.validate.name_length')}`
                        }"
                    />
                    <div class="text-danger mb-3" v-if="checkDuplicateSlugAdd(dataAction.slug)">
                        {{ $t('workflow.action.validate.duplicate_slug') }}
                    </div>
                    <FormKit
                        type="textarea"
                        :label="$t('workflow.action.description')"
                        :floating-label="false"
                        v-model="dataAction.description"
                        validation="length:1,200"
                        :validation-messages="{
                            length: `${$t('workflow.action.description')} ${$t('workflow.action.validate.description_length')}`
                        }"
                    />
                </FormKit>
                <CCardFooter>
                    <div class="d-flex justify-content-end">
                        <CButton 
                            type="button"
                            class="btn btn-light border m-1"
                            @click="closeFormAction"
                        >
                            <span class="text-uppercase">
                                {{ $t('workflow.action.close') }}
                            </span>
                        </CButton>
                        <CButton 
                            type="button"
                            class="btn btn-primary m-1"
                            @click.prevent="submitFormAction"
                        >
                            <span class="text-uppercase">
                                {{ $t('workflow.action.save_update') }}
                            </span>
                        </CButton>
                    </div>
                </CCardFooter>
            </BTab>
            <BTab :title="$t('workflow.action.list')">
                <CTable align="middle" responsive>
                    <table class="table table-hover">
                        <tbody v-if="listDataActions.length > 0">
                            <tr 
                                v-for="(action, index) in listDataActions" 
                                :key="index"
                            >
                                <td class="align-middle">{{ action.value.name }}</td> 
                                <td class="align-middle col-sm-1 table__td--action">
                                    <svg @click="editAction(index, action.value)"  class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M14.06 9.02l.92.92L5.92 19H5v-.92l9.06-9.06M17.66 3c-.25 0-.51.1-.7.29l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.2-.2-.45-.29-.71-.29zm-3.6 3.19L3 17.25V21h3.75L17.81 9.94l-3.75-3.75z"/>
                                    </svg>    
                                    <svg @click="removeAction(index)" class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                                    </svg>  
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr>
                                <td colspan="2" class="align-middle text-center">
                                    {{ $t('search.no_matching_records_found') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </CTable>
                <BAccordion v-if="state.activeTabEdit">
                    <BAccordionItem :title="$t('workflow.action.edit')" visible>
                        <FormKit
                            ref="formAction"
                            type="form"
                            :actions="false"
                            incomplete-message=" "
                            @submit="handleSubmitEditAction"
                        >
                            <FormKit
                                label-class="required-label" 
                                type="text"
                                :label="$t('workflow.action.name')"
                                :floating-label="false"
                                v-model="state.actionDetail.name"
                                validation="required|length:1,100"
                                @input="(value) => handleInputNameEdit(value)"
                                :validation-messages="{
                                    required: `${$t('workflow.action.name')} ${$t('workflow.action.validate.required')}`,
                                    length: `${$t('workflow.action.name')} ${$t('workflow.action.validate.name_length')}`
                                }"
                            />
                            <FormKit
                                label-class="required-label" 
                                type="text"
                                :label="$t('workflow.action.slug')"
                                :floating-label="false"
                                v-model="state.actionDetail.slug"
                                validation="required|length:1,100"
                                :readonly="true"
                                :validation-messages="{
                                    required: `${$t('workflow.action.slug')} ${$t('workflow.action.validate.required')}`,
                                    length: `${$t('workflow.action.slug')} ${$t('workflow.action.validate.name_length')}`
                                }"
                            />
                            <div class="text-danger mb-3" v-if="checkDuplicateSlugEdit(state.actionDetail.slug, state.indexEdit)">
                                {{ $t('workflow.action.validate.duplicate_slug') }}
                            </div>
                            <FormKit
                                type="textarea"
                                :label="$t('workflow.action.description')"
                                :floating-label="false"
                                v-model="state.actionDetail.description"
                                validation="length:1,200"
                                :validation-messages="{
                                    length: `${$t('workflow.action.description')} ${$t('workflow.action.validate.description_length')}`
                                }"
                            />
                        </FormKit>
                        <CCardFooter>
                            <div class="d-flex justify-content-end">
                                <CButton 
                                    type="button"
                                    class="btn btn-light border m-1"
                                    @click="closeEditAction"
                                >
                                    <span class="text-uppercase">
                                        {{ $t('workflow.action.close') }}
                                    </span>
                                </CButton>
                                <CButton 
                                    type="button"
                                    class="btn btn-primary m-1"
                                    @click.prevent="submitFormAction"
                                >
                                    <span class="text-uppercase">
                                        {{ $t('workflow.action.save_update') }}
                                    </span>
                                </CButton>
                            </div>
                        </CCardFooter>
                    </BAccordionItem>
                </BAccordion>
            </BTab>
        </BTabs>
    </CCol>
</template>

<script lang="ts">
import { defineComponent, ref, reactive } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import cloneDeep from 'lodash/cloneDeep'
import  { generateStandardSlug } from "@/utils/utils";

export default defineComponent({
    name: 'ActionAdd',
    emits: ['close-modal-action', 'reset-modal-action', 'add-action', 'edit-action', 'remove-action'],

    props: {
        dataAction: {
            type: Object,
            default: {},
            required: true,
        },
        listDataActions: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const formAction: any = ref(null);

        const state = reactive({
            tabIndex: 0,
            activeTabEdit: false,
            indexEdit: null as any,
            actionDetail: {} as any,
        });

        const submitFormAction = () => {
			const node = formAction.value.node;
			node.submit();
		}

        const closeFormAction = () => {
            emit('close-modal-action');
		}

        const handleSubmitFormAction = async () => {
            if (!checkDuplicateSlugAdd(props.dataAction.slug)) {
                emit("add-action", props.dataAction);
                emit('reset-modal-action');
                state.tabIndex = 1;
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const editAction = (index: number, action: object): void => {
            state.indexEdit = index;
            state.actionDetail = cloneDeep(action);
            state.activeTabEdit = true;
		}

        const removeAction = (index: number): void => {
            const slugValue = props.listDataActions[index].value.slug;
			props.listDataActions.splice(index, 1);
            emit("remove-action", props.listDataActions, slugValue);
            closeEditAction();
		}

        const closeEditAction = () => {
            state.activeTabEdit = false;
        }

        const handleSubmitEditAction = async () => {
            if (!checkDuplicateSlugEdit(state.actionDetail.slug, state.indexEdit)) {
                const slugValue = props.listDataActions[state.indexEdit].value.slug;
                emit("edit-action", state.actionDetail, state.indexEdit, slugValue);
                closeEditAction();
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const handleInputNameAdd = async (valueName: any) => {
            if (!valueName) {
                props.dataAction.slug = '';
			} else {
    			props.dataAction.slug = generateStandardSlug(valueName);
			}
		}

        const handleInputNameEdit = async (valueName: any) => {
            if (!valueName) {
                state.actionDetail.slug = '';
			} else {
    			state.actionDetail.slug = generateStandardSlug(valueName);
			}
		}

        const checkDuplicateSlugAdd = (slug: string): boolean => {
            return props.listDataActions.some((item: any) => item.value.slug === slug);
        }

        const checkDuplicateSlugEdit = (slug: string, indexEdit: number): boolean => {
            return props.listDataActions.some((item: any, index: number) => {
                // Bỏ qua kiểm tra nếu là chính slug hiện tại (indexEdit)
                if (index === indexEdit) {
                    return false;
                }
                return item.value.slug === slug;
            });
        }

        return {
            state,
            formAction,
            submitFormAction,
            closeFormAction,
            handleSubmitFormAction,
            editAction,
            removeAction,
            closeEditAction,
            handleSubmitEditAction,
            handleInputNameAdd,
            handleInputNameEdit,
            checkDuplicateSlugAdd,
            checkDuplicateSlugEdit
        }
    },
});
</script>
<style type="text/css" scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color:#f8f9fa;
    padding: 10px;
}
svg {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
</style>