import { useI18n } from "vue-i18n";

interface FieldValue {
    id: number;
    label: string;
    field?: {
        type: string;
    };
    extracted_values?: any[];
    field_value?: any;
    table_values?: any[];
    table_columns_type?: Record<string, string>;
}

/**
 * Composable for handling job field values display logic
 * Extracted from JobDetail.vue for reusability
 */
export default function useJobFieldValues() {
    const { t } = useI18n();

    /**
     * Get file list from field value
     * @param fieldValue - The field value object
     * @returns Array of files
     */
    const getFileList = (fieldValue: FieldValue): Array<any> => {
        const files = fieldValue.extracted_values?.length 
            ? fieldValue.extracted_values 
            : fieldValue.field_value;

        if (!files) return [];
        return Array.isArray(files) ? files : [files];
    };

    /**
     * Get formatted field value for display
     * @param fieldValue - The field value object
     * @returns Formatted string value
     */
    const getFieldValue = (fieldValue: FieldValue): string => {
        if (fieldValue.field?.type === 'FILEUPLOAD') {
            return '';
        }

        // Xử lý trường hợp OBJECTSYSTEM
        if (fieldValue.field?.type === 'OBJECTSYSTEM') {
            const values = fieldValue.extracted_values?.length 
                ? fieldValue.extracted_values 
                : fieldValue.field_value;
            
            if (Array.isArray(values)) {
                // Nếu là mảng các đối tượng có thuộc tính main
                if (values.length > 0 && typeof values[0] === 'object') {
                    return values
                        .map(item => item.main || '')
                        .filter(Boolean)
                        .join(', ');
                }
                // Nếu là mảng các giá trị chuỗi
                return values.join(', ');
            } else if (typeof values === 'object' && values !== null) {
                // Nếu là đối tượng có thuộc tính main (chỉ áp dụng cho field_value)
                return values.main || t('common.no_data');
            } else if (values) {
                return values.toString();
            }
        }

        // Xử lý cho các trường hợp khác
        const values = fieldValue.extracted_values?.length 
            ? fieldValue.extracted_values 
            : fieldValue.field_value;
        
        if (!values) {
            return t('common.no_data');
        }
        
        if (Array.isArray(values)) {
            // Nếu là mảng các đối tượng có thuộc tính main
            if (values.length > 0 && typeof values[0] === 'object') {
                return values
                    .map(item => item.main || '')
                    .filter(Boolean)
                    .join(', ');
            }
            // Nếu là mảng các giá trị chuỗi
            return values.join(', ');
        } else if (typeof values === 'object' && values !== null) {
            // Nếu là đối tượng có thuộc tính main (chỉ áp dụng cho field_value)
            return values.main || t('common.no_data');
        }
        
        return values.toString();
    };

    /**
     * Format table cell value for display
     * @param value - The cell value
     * @returns Formatted string value
     */
    const formatTableValue = (value: any): string => {
        if (Array.isArray(value)) {
            // Nếu là mảng các đối tượng có thuộc tính main
            if (value.length > 0 && typeof value[0] === 'object') {
                return value
                    .map(item => item.main || '')
                    .filter(Boolean)
                    .join(', ');
            }
            return value.join(', ');
        } else if (typeof value === 'object' && value !== null) {
            // Nếu là đối tượng có thuộc tính main
            return value.main || t('common.no_data');
        }
        return value || t('common.no_data');
    };

    /**
     * Check if table column is file upload type
     * @param fieldValue - The field value object
     * @param columnKey - The column key
     * @returns Boolean indicating if column is file upload type
     */
    const isColumnTypeFileUpload = (fieldValue: FieldValue, columnKey: string | number): boolean => {
        // Kiểm tra xem có table_columns_type không
        if (!fieldValue.table_columns_type) return false;
        
        // Chuyển key thành chuỗi để đảm bảo
        const key = columnKey.toString();
        
        // Tìm loại cột dựa vào key
        const columnType = fieldValue.table_columns_type[key];
        
        // Kiểm tra xem cột có phải loại FILEUPLOAD không
        return columnType === 'FILEUPLOAD';
    };

    /**
     * Convert value to file array
     * @param value - The value to convert
     * @returns Array of file strings
     */
    const getFileArrayFromValue = (value: any): string[] => {
        if (!value) return [];
        
        if (typeof value === 'string') {
            return [value];
        }
        
        if (Array.isArray(value)) {
            // Đảm bảo chỉ trả về các phần tử là chuỗi
            return value.filter(item => typeof item === 'string') as string[];
        }
        
        return [];
    };

    return {
        getFileList,
        getFieldValue,
        formatTableValue,
        isColumnTypeFileUpload,
        getFileArrayFromValue
    };
}
