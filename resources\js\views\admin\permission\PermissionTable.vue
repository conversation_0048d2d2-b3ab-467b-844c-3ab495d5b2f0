<template>
    <CTable align="middle" responsive>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th class="align-middle">
                        {{ $t('permission.name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('permission.slug') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('permission.is_active') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('permission.is_hidden') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('permission.permission_group') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('permission.create_by') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('permission.created_at') }}
                    </th>
                </tr>
            </thead>
            <tbody v-if="checkDataNotEmpty">
                <tr v-for="(permission, index) in dataPermissions" :key="index">
                    <td class="align-middle">
                        {{ permission.name }}
                    </td>
                    <td class="align-middle">
                        {{ permission.slug }}
                    </td>
                    <td class="align-middle">
                        <span class="badge rounded-pill bg-success" v-if="permission.is_active === PERMISSION_STATUS.TRUE">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_permission.active') }}
                            </small>
                        </span>
                        <span class="badge rounded-pill bg-danger" v-else-if="permission.is_active === PERMISSION_STATUS.FALSE">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_permission.unactive') }}
                            </small>
                        </span>
                    </td>
                    <td class="align-middle">
                        <span class="badge rounded-pill bg-success" v-if="permission.is_hidden === PERMISSION_STATUS.TRUE">
                            <small class="fst-normal text-white">
                                {{ $t('permission.show') }}
                            </small>
                        </span>
                        <span class="badge rounded-pill bg-danger" v-else-if="permission.is_hidden === PERMISSION_STATUS.FALSE">
                            <small class="fst-normal text-white">
                                {{ $t('permission.hidden') }}
                            </small>
                        </span>
                    </td>
                    <td class="align-middle">
                        {{ permission.permission_group.name }}
                    </td>
                    <td class="align-middle">
                        {{ permission.create_by.full_name }}
                    </td>
                    <td class="align-middle">
                        {{ formatDate(permission.created_at) }}
                    </td>
                </tr>
            </tbody>
            <tbody v-else>
                <tr>
                    <td colspan="7" class="align-middle text-center">
                        {{ $t('search.no_matching_records_found') }}
                    </td>
                </tr>
            </tbody>
        </table>
    </CTable>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { PERMISSION_STATUS } from "@/constants/constants";
import moment from 'moment'

export default defineComponent({
    name: 'PermissionTable',
    emits: ['update-data-paginate'],

    props: {
        dataPermissions: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const checkDataNotEmpty = computed<boolean>(() => {
            return props.dataPermissions.length > 0;
        });
        
        const formatDate = (date: string) => {
            return moment(date).format('DD/MM/YYYY');
        };

        return {
            checkDataNotEmpty,
            PERMISSION_STATUS,
            formatDate,
        }
    },
});
</script>
<style type="text/css" scoped>
</style>