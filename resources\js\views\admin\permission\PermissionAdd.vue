<template>
    <CRow>
        <CCol :xs="12">
            <CCardBody>
                <CForm @submit.prevent="savePermission" class="row gx-3 gy-3 align-items-center">
                    <!-- Permission Name -->
                    <CCol :md="12">
                        <CFormLabel>
                            {{ $t('permission.name') }} 
                            <span class="text-danger">*</span>
                        </CFormLabel>
                        <CInputGroup>
                            <CFormInput
                                type="text"
                                v-model="state.form.name"
                                :placeholder="$t('permission.name')"
                                required
                            />
                        </CInputGroup>
                    </CCol>

                    <!-- Permission Slug -->
                    <CCol :md="12">
                        <CFormLabel>
                            {{ $t('permission.slug') }} 
                            <span class="text-danger">*</span>
                        </CFormLabel>
                        <CInputGroup>
                            <CFormInput
                                type="text"
                                v-model="state.form.slug"
                                :placeholder="$t('permission.slug')"
                                required
                            />
                        </CInputGroup>
                    </CCol>

                    <!-- Permission Group -->
                    <CCol :md="12">
                        <div class="d-flex align-items-center">
                            <label class="mb-1">
                                {{ $t('permission.permission_group') }} 
                                <span class="text-danger">*</span>
                            </label>
                            <span 
                                class="material-symbols-outlined ms-1 cursor-pointer"
                                v-b-tooltip.hover
                                @click="state.showGroupModal = true"
                            >
                                add_circle
                            </span>
                        </div>
                        <CInputGroup>
                            <CFormSelect
                                v-model="state.form.permission_group_id"
                                required
                            >
                                <option value="">{{ $t('permission.permission_group') }}</option>
                                <option 
                                    v-for="group in state.permissionGroups" 
                                    :key="group.id" 
                                    :value="group.id"
                                >
                                    {{ group.name }}
                                </option>
                            </CFormSelect>
                        </CInputGroup>
                    </CCol>

                    <!-- Is Active -->
                    <CCol :md="6">
                        <CFormLabel>
                            {{ $t('permission.is_active') }}
                        </CFormLabel>
                        <CInputGroup>
                            <select
                                class="form-select"
                                v-model="state.form.is_active"
                                required
                            >
                                <option :value="PERMISSION_STATUS.TRUE">
                                    {{ $t('option_tab_permission.active') }}
                                </option>
                                <option :value="PERMISSION_STATUS.FALSE">
                                    {{ $t('option_tab_permission.unactive') }}
                                </option>
                            </select>
                        </CInputGroup>
                    </CCol>

                    <!-- Is Hidden -->
                    <CCol :md="6">
                        <CFormLabel>
                            {{ $t('permission.is_hidden') }}
                        </CFormLabel>
                        <CInputGroup>
                            <select
                                class="form-select"
                                v-model="state.form.is_hidden"
                                required
                            >   
                                <option :value="PERMISSION_STATUS.TRUE">
                                    {{ $t('permission.show') }}
                                </option>
                                <option :value="PERMISSION_STATUS.FALSE">
                                    {{ $t('permission.hidden') }}
                                </option>
                            </select>
                        </CInputGroup>
                    </CCol>

                    <!-- Submit Button -->
                    <CCardFooter>
                        <div class="d-flex justify-content-end">
                            <CButton 
                                type="button"
                                class="btn btn-light border m-1"
                                @click="closeModalPermission"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.stage.close') }}
                                </span>
                            </CButton>
                            <CButton 
                                type="submit"
                                class="btn btn-primary m-1"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.stage.save_update') }}
                                </span>
                            </CButton>
                        </div>
                    </CCardFooter>
                </CForm>
            </CCardBody>
        </CCol>
    </CRow>
    
    <!-- Add Permission Group Modal -->
    <CModal
        :visible="state.showGroupModal"
        @close="() => state.showGroupModal = false"
        backdrop="static"
    >
        <CModalHeader>
            <CModalTitle>{{ $t('permission.add_permission_group') }}</CModalTitle>
        </CModalHeader>
        <CModalBody>
            <CForm @submit.prevent="savePermissionGroup">
                <CFormLabel>
                    {{ $t('permission.group_name') }} 
                    <span class="text-danger">*</span>
                </CFormLabel>
                <CFormInput
                    type="text"
                    v-model="state.newGroupForm.name"
                    required
                />
                <CModalFooter>
                    <CButton 
                        color="secondary" 
                        @click="() => state.showGroupModal = false"
                    >
                        {{ $t('workflow.stage.close') }}
                    </CButton>
                    <CButton 
                        color="primary"
                        type="submit"
                    >
                        {{ $t('workflow.stage.save_update') }}
                    </CButton>
                </CModalFooter>
            </CForm>
        </CModalBody>
    </CModal>

    <loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue'
import Loading from '@/views/loading/Loading.vue'
import usePermissions from '@/composables/permission'
import { PERMISSION_STATUS } from "@/constants/constants"
import { useToast } from 'vue-toast-notification'
import { useI18n } from "vue-i18n"

export default defineComponent({
    name: 'PermissionAdd',
    emits: ['close-modal', 'permission-added'],

    components: {
        Loading
    },

    setup(props: any, {emit}) {
        const $toast = useToast()
        const { t } = useI18n()
        
        const state = reactive({
            form: {
                name: '',
                slug: '',
                permission_group_id: '',
                is_active: PERMISSION_STATUS.TRUE,
                is_hidden: PERMISSION_STATUS.TRUE,
            },
            newGroupForm: {
                name: ''
            },
            permissionGroups: [] as any[],
            showGroupModal: false
        })

        const { setIsLoading, storePermission, getPermissionGroups, storePermissionGroup } = usePermissions()

        const loadPermissionGroups = async (): Promise<void> => {
            try {
                const response = await getPermissionGroups()
                if (response && response.status === 'success') {
                    state.permissionGroups = response.permission_groups
                }
            } catch (error) {
                console.error('Error loading permission groups:', error)
            }
        }

        const savePermission = async (): Promise<void> => {
            try {
                const result = await storePermission({...state.form})
                if (result && result.status === 'success') {
                    $toast.open({
                        message: t('toast.status.ACTION_SUCCESS'),
                        type: "success",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    })
                    
                    resetFormPermission()
                    emit('permission-added')
                    emit('close-modal')
                }
            } catch (error) {
                console.error('Error saving permission:', error)
            }
        }

        const savePermissionGroup = async (): Promise<void> => {
            try {
                const result = await storePermissionGroup({...state.newGroupForm})
                if (result && result.status === 'success') {
                    $toast.open({
                        message: t('toast.status.ACTION_SUCCESS'),
                        type: "success",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    })
                    
                    state.showGroupModal = false;
                    resetFormPermissionGroup()
                    loadPermissionGroups()
                }
            } catch (error) {
                console.error('Error saving permission group:', error)
            }
        }

        const resetFormPermission = (): void => {
            state.form = {
                name: '',
                slug: '',
                permission_group_id: '',
                is_active: PERMISSION_STATUS.TRUE,
                is_hidden: PERMISSION_STATUS.FALSE,
            }
        }

        const resetFormPermissionGroup = (): void => {
            state.newGroupForm = {
                name: ''
            }
        }

        onMounted( async () => {
            await loadPermissionGroups();
        })

        const closeModalPermission = (): void => {
            emit('close-modal')
        }

        return {
            state,
            setIsLoading,
            savePermission,
            savePermissionGroup,
            PERMISSION_STATUS,
            closeModalPermission
        }
    },
})
</script>

<style type="text/css" scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color:#f8f9fa;
    padding: 10px;
}
.cursor-pointer {
    cursor: pointer;
}
</style>