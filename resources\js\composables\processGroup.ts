import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";

export default function useProcessGroups() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const storeProcessGroup = async (formProcessGroupData: any) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/process-group`, formProcessGroupData);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storeProcessGroup(formProcessGroupData);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    return {
        setIsLoading,
        storeProcessGroup,
    }
}