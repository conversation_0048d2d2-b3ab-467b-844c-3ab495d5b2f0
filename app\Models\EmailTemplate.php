<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class EmailTemplate extends Model
{
    use HasUuids;
    
    protected $table = 'email_templates';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'slug',
        'name_title',
        'content',
        'from_email',
        'to_emails',
        'cc_emails',
        'bcc_emails',
        'files',
        'process_version_id',
    ];
    
    protected $casts = [
        'to_emails' => 'array',
        'cc_emails' => 'array',
        'bcc_emails' => 'array',
        'files' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
