<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Traits\ActiveGlobalScopeTrait;

class JobPosition extends Model
{
    use ActiveGlobalScopeTrait, HasUuids;
    protected $table = 'job_positions';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'tenant_id',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
