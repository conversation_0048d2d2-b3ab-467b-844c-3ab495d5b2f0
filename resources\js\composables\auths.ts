import { ref, reactive } from 'vue'
import axios from 'axios';
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toast-notification';

export default function useAuths() {
    const router      = useRouter();
    const $toast      = useToast();

    const setIsLoading = ref(false);

    const user = reactive({
        account_name: '',
        name: '',
        email: '',
        language: '',
    }) as any;

    const storeAuth = async (data: object) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post('/api/auth/login', data);
            setToken(response.data.access_token);
            window.location.href = "/";
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storeAuth(data);
                }, 1000);

                return;
            }
            switch (error.response.status) {
                case 401:
					$toast.open({
                        message: error.response.data.message,
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "top",
                    });
					break;
				case 500:
					clearDataUser();
					break;
				default:
					break;
			}
        } finally {
            setIsLoading.value = false;
        } 
    }

    const setToken = (access_token: string): void => {
        localStorage.setItem('tvnas_session_id', access_token);
    }

    const resendOTP = async () => {
        try {
            let response = await axios.get('/api/resend-otp');
            $toast.open({
                message: response.data.message,
                type: "success",
                duration: 5000,
                dismissible: true,
                position: "top",
            });
        } catch (error: any) {
            switch (error.response.status) {
				case 500:
					clearDataUser();
					break;
				default:
					break;
			}
        }
    }

    const getUser = async (): Promise<void> => {
        try {
            let response = await axios.get('/api/auth/user');
            user.account_name = response.data.account_name;
            user.name         = response.data.name;
            user.email        = response.data.email;
            user.language     = response.data.language;
        } catch (error: any) {
            switch (error.response.status) {
				case 500:
					clearDataUser();
					break;
				default:
					break;
			}
        }
    }
    
    const logout = async (): Promise<void> => {
        await axios.get('/api/auth/logout');
        clearDataUser();
        window.location.href = "/login";
    }

    const clearDataUser = () => {
        localStorage.clear();
        location.reload();
    }

    const resetPassword = async (valueEmail: string) => {
        try {
            let response = await axios.post('/api/auth/reset-password', {
                email: valueEmail,
            });
            if (response.data.status == true) {
                $toast.open({
                    message: response.data.message,
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "top",
                });
            } else {
                $toast.open({
                    message: response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "top",
                });
            }
        } catch (error: any) {
            console.log(error);
        }
    }

    const updateNewPassword = async (valueToken: string, valueNewPassword: string, valueNewPasswordConfirm: string) => {
        try {
            let response = await axios.post('/api/auth/new-password', {
                token: valueToken,
                new_password: valueNewPassword,
                new_password_confirm: valueNewPasswordConfirm,
            });
            $toast.open({
                message: response.data.message,
                type: "success",
                duration: 5000,
                dismissible: true,
                position: "top",
            });
            await router.push('/login');
        } catch (error: any) {
            switch (error.response.status) {
                case 422:
                    if (error.response.data.errors.new_password) {
                        $toast.open({
                            message: error.response.data.errors.new_password[0],
                            type: "error",
                            duration: 5000,
                            dismissible: true,
                            position: "top",
                        });
                        break;
                    }
                    if (error.response.data.errors.new_password_confirm) {
                        $toast.open({
                            message: error.response.data.errors.new_password_confirm[0],
                            type: "error",
                            duration: 5000,
                            dismissible: true,
                            position: "top",
                        });
                        break;
                    }
                case 401:
                    $toast.open({
                        message: error.response.data.message,
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "top",
                    });
                    await router.push('/reset-password');
                    break;
                default:
                    break;
            }
        }
    }

    const updateChangePassword = async (data: object) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post('/api/change-password', data);
            $toast.open({
                message: response.data.message,
                type: "success",
                duration: 5000,
                dismissible: true,
                position: "top",
            });

            return true;
        } catch (error: any) {
            switch (error.response.status) {
                case 422:
                    if (error.response.data.errors.current_password) {
                        $toast.open({
                            message: error.response.data.errors.current_password[0],
                            type: "error",
                            duration: 5000,
                            dismissible: true,
                            position: "top",
                        });
                        break;
                    }
                    if (error.response.data.errors.new_password) {
                        $toast.open({
                            message: error.response.data.errors.new_password[0],
                            type: "error",
                            duration: 5000,
                            dismissible: true,
                            position: "top",
                        });
                        break;
                    }
                    if (error.response.data.errors.new_password_confirm) {
                        $toast.open({
                            message: error.response.data.errors.new_password_confirm[0],
                            type: "error",
                            duration: 5000,
                            dismissible: true,
                            position: "top",
                        });
                        break;
                    }
                case 400:
                    $toast.open({
                        message: error.response.data.message,
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "top",
                    });
                    break;
                default:
                    break;
            }
        } finally {
            setIsLoading.value = false;
        }
    }

    const updateChangeLanguage = async (valueLanguage: string) => {
        try {
            await axios.post('/api/change-language', {
                language: valueLanguage,
            });
            location.reload();
        } catch (error: any) {
            console.log(error);
        }
    }
    

    return {
        setIsLoading,
        user,
        storeAuth,
        resendOTP,
        getUser,
        logout,
        resetPassword,
        updateNewPassword,
        updateChangePassword,
        updateChangeLanguage
    }
}