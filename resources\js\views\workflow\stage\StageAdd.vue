<template>
    <CCol :xs="12">
        <BTabs no-body content-class="mt-3" v-model="state.tabIndex">
            <BTab :title="$t('workflow.stage.create')">
                <Form ref="form" @submit="handleSubmitFormStage" :validation-schema="schemaCreate()">
                    <CCol :xs="12" class="mb-3">
                        <div class="form-check form-check-inline">
                            <input
                                v-model="dataStage.comment"
                                name="comment" 
                                type="checkbox" 
                                true-value="yes"
                                false-value=""
                                class="form-check-input p-2" 
                            />
                            <label class="form-check-label ms-1">
                                {{ $t('workflow.stage.comment') }}
                            </label>
                        </div>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.stage.name') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataStage.name"
                            name="name" 
                            type="text" 
                            class="form-control" 
                            maxlength="200" 
                            @change="handleInputNameAdd(dataStage.name)"
                        />
                        <ErrorMessage
                            as="div"
                            name="name"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.stage.slug') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataStage.slug"
                            name="slug" 
                            type="text" 
                            class="form-control" 
                            maxlength="200" 
                            :readonly="true"
                        />
                        <ErrorMessage
                            as="div"
                            name="slug"
                            class="text-danger"
                        />
                        <div class="text-danger mb-3" v-if="checkDuplicateSlugAdd(dataStage.slug)">
                            {{ $t('workflow.stage.validate.duplicate_slug') }}
                        </div>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <div class="d-flex align-items-center">
                            <label class="mb-1">
                                {{ $t('workflow.stage.approver') }}
                                <span class="text-danger">*</span>
                            </label>
                            <span 
                                class="material-symbols-outlined ms-1 cursor-pointer icon-info"
                                v-b-tooltip.hover
                                :title="$t('workflow.stage.approver_desc')"
                            >
                                info
                            </span>
                        </div>
                        <Field 
                            name="approver"
                            v-slot="{ field }"
                        >
                            <Multiselect
                                mode="tags"
                                v-bind="field"
                                v-model="dataStage.approver"
                                :placeholder="$t('workflow.choose')"
                                :close-on-select="false"
                                :filter-results="false"
                                :resolve-on-load="false"
                                :infinite="true"
                                :limit="20"
                                :clear-on-search="true"
                                :searchable="true"
                                :delay="0"
                                :min-chars="0"
                                :object="true"
                                :options="async (query) => {
                                    return await debouncedGetOptionScopes(query)
                                }"
                                @open="debouncedGetOptionScopes('')"
                                :can-clear="false"
                            >
                                <template v-slot:option="{ option }">
                                    <div class="custom-option">
                                        <div class="option-label mb-1">
                                            {{ option.label }}
                                        </div>
                                        <div class="option-description text-secondary">
                                            <small>
                                                <i>{{ option.description }}</i>
                                            </small>
                                        </div>
                                    </div>
                                </template>
                            </Multiselect>
                        </Field>
                        <ErrorMessage
                            as="div"
                            name="approver"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <div class="d-flex align-items-center">
                            <label class="mb-1">
                                {{ $t('workflow.stage.follower') }}
                            </label>
                            <span 
                                class="material-symbols-outlined ms-1 cursor-pointer icon-info"
                                v-b-tooltip.hover
                                :title="$t('workflow.stage.follower_desc')"
                            >
                                info
                            </span>
                        </div>
                        <Multiselect
                            mode="tags"
                            v-model="dataStage.followers"
                            :placeholder="$t('workflow.choose')"
                            :close-on-select="false"
                            :filter-results="false"
                            :resolve-on-load="false"
                            :infinite="true"
                            :limit="20"
                            :clear-on-search="true"
                            :searchable="true"
                            :delay="0"
                            :min-chars="0"
                            :object="true"
                            :options="async (query) => {
                                return await debouncedGetOptionScopes(query)
                            }"
                            @open="debouncedGetOptionScopes('')"
                            :can-clear="false"
                        >
                            <template v-slot:option="{ option }">
                                <div class="custom-option">
                                    <div class="option-label mb-1">
                                        {{ option.label }}
                                    </div>
                                    <div class="option-description text-secondary">
                                        <small>
                                            <i>{{ option.description }}</i>
                                        </small>
                                    </div>
                                </div>
                            </template>
                        </Multiselect>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.stage.description') }}
                        </label>
                        <Field 
                            v-model="dataStage.description"
                            name="description"
                            as="textarea"
                            class="form-control" 
                            :maxlength="500" 
                            rows="2"
                        />
                    </CCol>
                    <CCardFooter>
                        <div class="d-flex justify-content-end">
                            <CButton 
                                type="button"
                                class="btn btn-light border m-1"
                                @click="closeFormStage"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.stage.close') }}
                                </span>
                            </CButton>
                            <CButton 
                                type="submit"
                                class="btn btn-primary m-1"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.stage.save_update') }}
                                </span>
                            </CButton>
                        </div>
                    </CCardFooter>
                </Form>
            </BTab>
            <BTab :title="$t('workflow.stage.list')">
                <CTable align="middle" responsive>
                    <table class="table table-hover">
                        <tbody v-if="listDataStages.length > 0">
                            <tr 
                                v-for="(stage, index) in listDataStages" 
                                :key="index"
                            >
                                <td class="align-middle">{{ stage.value.name }}</td> 
                                <td class="align-middle col-sm-1 table__td--action">
                                    <svg @click="editStage(index, stage.value)"  class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M14.06 9.02l.92.92L5.92 19H5v-.92l9.06-9.06M17.66 3c-.25 0-.51.1-.7.29l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.2-.2-.45-.29-.71-.29zm-3.6 3.19L3 17.25V21h3.75L17.81 9.94l-3.75-3.75z"/>
                                    </svg>    
                                    <svg @click="removeStage(index)" class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                                    </svg>  
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr>
                                <td colspan="2" class="align-middle text-center">
                                    {{ $t('search.no_matching_records_found') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </CTable>
                <BAccordion v-if="state.activeTabEdit">
                    <BAccordionItem :title="$t('workflow.stage.edit')" visible>
                        <Form ref="form" @submit="handleSubmitEditStage" :validation-schema="schemaEdit()">
                            <CCol :xs="12" class="mb-3">
                                <div class="form-check form-check-inline">
                                    <input
                                        v-model="state.stageDetail.comment"
                                        name="comment" 
                                        type="checkbox" 
                                        true-value="yes"
                                        false-value=""
                                        class="form-check-input p-2" 
                                    />
                                    <label class="form-check-label ms-1">
                                        {{ $t('workflow.stage.comment') }}
                                    </label>
                                </div>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.stage.name') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.stageDetail.name"
                                    name="name" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="200" 
                                    @change="handleInputNameEdit(state.stageDetail.name)"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="name"
                                    class="text-danger"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.stage.slug') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.stageDetail.slug"
                                    name="slug" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="200" 
                                    :readonly="true"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="slug"
                                    class="text-danger"
                                />
                                <div class="text-danger mb-3" v-if="checkDuplicateSlugEdit(state.stageDetail.slug, state.indexEdit)">
                                    {{ $t('workflow.stage.validate.duplicate_slug') }}
                                </div>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <div class="d-flex align-items-center">
                                    <label class="mb-1">
                                        {{ $t('workflow.stage.approver') }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <span 
                                        class="material-symbols-outlined ms-1 cursor-pointer icon-info"
                                        v-b-tooltip.hover
                                        :title="$t('workflow.stage.approver_desc')"
                                    >
                                        info
                                    </span>
                                </div>
                                <Field 
                                    name="approver"
                                    v-slot="{ field }"
                                >
                                    <Multiselect
                                        mode="tags"
                                        v-bind="field"
                                        v-model="state.stageDetail.approver"
                                        :placeholder="$t('workflow.choose')"
                                        :close-on-select="false"
                                        :filter-results="false"
                                        :resolve-on-load="false"
                                        :infinite="true"
                                        :limit="20"
                                        :clear-on-search="true"
                                        :searchable="true"
                                        :delay="0"
                                        :min-chars="0"
                                        :object="true"
                                        :options="async (query) => {
                                            return await debouncedGetOptionScopes(query)
                                        }"
                                        @open="debouncedGetOptionScopes('')"
                                        :can-clear="false"
                                    >
                                        <template v-slot:option="{ option }">
                                            <div class="custom-option">
                                                <div class="option-label mb-1">
                                                    {{ option.label }}
                                                </div>
                                                <div class="option-description text-secondary">
                                                    <small>
                                                        <i>{{ option.description }}</i>
                                                    </small>
                                                </div>
                                            </div>
                                        </template>
                                    </Multiselect>
                                </Field>
                                <ErrorMessage
                                    as="div"
                                    name="approver"
                                    class="text-danger"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <div class="d-flex align-items-center">
                                    <label class="mb-1">
                                        {{ $t('workflow.stage.follower') }}
                                    </label>
                                    <span 
                                        class="material-symbols-outlined ms-1 cursor-pointer icon-info"
                                        v-b-tooltip.hover
                                        :title="$t('workflow.stage.follower_desc')"
                                    >
                                        info
                                    </span>
                                </div>
                                <Multiselect
                                    mode="tags"
                                    v-model="state.stageDetail.followers"
                                    :placeholder="$t('workflow.choose')"
                                    :close-on-select="false"
                                    :filter-results="false"
                                    :resolve-on-load="false"
                                    :infinite="true"
                                    :limit="20"
                                    :clear-on-search="true"
                                    :searchable="true"
                                    :delay="0"
                                    :min-chars="0"
                                    :object="true"
                                    :options="async (query) => {
                                        return await debouncedGetOptionScopes(query)
                                    }"
                                    @open="debouncedGetOptionScopes('')"
                                    :can-clear="false"
                                >
                                    <template v-slot:option="{ option }">
                                        <div class="custom-option">
                                            <div class="option-label mb-1">
                                                {{ option.label }}
                                            </div>
                                            <div class="option-description text-secondary">
                                                <small>
                                                    <i>{{ option.description }}</i>
                                                </small>
                                            </div>
                                        </div>
                                    </template>
                                </Multiselect>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.stage.description') }}
                                </label>
                                <Field 
                                    v-model="state.stageDetail.description"
                                    name="description"
                                    as="textarea"
                                    class="form-control" 
                                    :maxlength="500" 
                                    rows="2"
                                />
                            </CCol>
                            <CCardFooter>
                                <div class="d-flex justify-content-end">
                                    <CButton 
                                        type="button"
                                        class="btn btn-light border m-1"
                                        @click="closeEditStage"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.stage.close') }}
                                        </span>
                                    </CButton>
                                    <CButton 
                                        type="submit"
                                        class="btn btn-primary m-1"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.stage.save_update') }}
                                        </span>
                                    </CButton>
                                </div>
                            </CCardFooter>
                        </Form>
                    </BAccordionItem>
                </BAccordion>
            </BTab>
        </BTabs>
    </CCol>
</template>

<script lang="ts">
import { defineComponent, ref, reactive } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import Multiselect from '@vueform/multiselect';
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import cloneDeep from 'lodash/cloneDeep'
import debounce from 'lodash.debounce';
import useOptions from '@/composables/option';
import  { generateStandardSlug } from "@/utils/utils";
import { WORKFLOWS } from "@/constants/constants";

export default defineComponent({
    name: 'StageAdd',
    emits: ['close-modal-stage', 'reset-modal-stage', 'add-stage', 'edit-stage', 'remove-stage'],

    components: {
        Multiselect,
        Form,
		Field,
		ErrorMessage,
    },

    props: {
        dataStage: {
            type: Object,
            default: {},
            required: true,
        },
        listDataStages: {
            type: Array as () => Array<any>,
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const schemaCreate = () => {
            let schemaForm = yup.object().shape({});

            schemaForm = schemaForm.shape({
                name: yup.string()
                    .required(`${t('workflow.stage.name')} ${t('workflow.stage.validate.required')}`),
                slug: yup.string()
                    .required(`${t('workflow.stage.slug')} ${t('workflow.stage.validate.required')}`),
                approver: yup.array()
                    .min(1, `${t('workflow.stage.approver')} ${t('workflow.validate.required')}`)
                    .required(`${t('workflow.stage.approver')} ${t('workflow.stage.validate.required')}`),
            });

            return schemaForm;
        }

        const schemaEdit = () => {
            let schemaForm = yup.object().shape({});

            schemaForm = schemaForm.shape({
                name: yup.string()
                    .required(`${t('workflow.stage.name')} ${t('workflow.stage.validate.required')}`),
                slug: yup.string()
                    .required(`${t('workflow.stage.slug')} ${t('workflow.stage.validate.required')}`),
                approver: state.stageDetail.approver?.length ? yup.array().nullable() : yup.array()
                    .min(1, `${t('workflow.stage.approver')} ${t('workflow.validate.required')}`)
                    .required(`${t('workflow.stage.approver')} ${t('workflow.stage.validate.required')}`),
            });

            return schemaForm;
        }

        const state = reactive({
            tabIndex: 0,
            activeTabEdit: false,
            indexEdit: null as any,
            stageDetail: {} as any,
            selectOptionSystemDefaults: [
				{ label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
			] as Array<any>,
        });

        const closeFormStage = () => {
            emit('close-modal-stage');
		}

        const handleSubmitFormStage = async () => {
            if (!checkDuplicateSlugAdd(props.dataStage.slug)) {
                emit("add-stage", props.dataStage);
                emit('reset-modal-stage');
                state.tabIndex = 1;
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const editStage = async (index: number, stage: object): Promise<void> => {
            state.indexEdit = index;
            state.stageDetail = cloneDeep(stage);

            // Format approver và followers để hiển thị trong Multiselect
            await formatStageDetailForEdit();

            state.activeTabEdit = true;
		}

        const removeStage = (index: number): void => {
            const slugValue = props.listDataStages[index].value.slug;
			props.listDataStages.splice(index, 1);
            emit("remove-stage", props.listDataStages, slugValue);
            closeEditStage();
		}

        const closeEditStage = () => {
            state.activeTabEdit = false;
        }

        const handleSubmitEditStage = async () => {
            if (!checkDuplicateSlugEdit(state.stageDetail.slug, state.indexEdit)) {
                const slugValue = props.listDataStages[state.indexEdit].value.slug;
                emit("edit-stage", state.stageDetail, state.indexEdit, slugValue);
                closeEditStage();
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const { getScopes } = useOptions();

        const getOptionProcessScopes = async (query: string) => {
            let result = await getScopes(query);
            if (Array.isArray(result) && result.length > 0) {
                // Kết hợp các option mặc định với kết quả trả về từ API
                return [...state.selectOptionSystemDefaults, ...result];
            }

            // Nếu không có kết quả từ API, chỉ trả về các option mặc định
            return [...state.selectOptionSystemDefaults];
		}

		const debouncedGetOptionScopes = debounce(getOptionProcessScopes, 500);

        const formatStageDetailForEdit = async (): Promise<void> => {
            try {
                // Format approver từ array ID thành array object
                if (state.stageDetail.approver && Array.isArray(state.stageDetail.approver) && state.stageDetail.approver.length > 0) {
                    if (!state.stageDetail.approver[0].label) {
                        const approverOptions = await getOptionProcessScopes('');
                        // Tìm các approver phù hợp từ approverOptions dựa trên ID
                        const matchedApprovers = approverOptions.filter((option: any) =>
                            state.stageDetail.approver.includes(option.value)
                        );
                        state.stageDetail.approver = matchedApprovers || [];
                    }
                } else {
                    state.stageDetail.approver = [];
                }

                // Format followers từ array ID thành array object
                if (state.stageDetail.followers && Array.isArray(state.stageDetail.followers) && state.stageDetail.followers.length > 0) {
                    if (!state.stageDetail.followers[0].label) {
                        const followerOptions = await getOptionProcessScopes('');
                        // Tìm các followers phù hợp từ followerOptions dựa trên ID
                        const matchedFollowers = followerOptions.filter((option: any) =>
                            state.stageDetail.followers.includes(option.value)
                        );
                        state.stageDetail.followers = matchedFollowers || [];
                    }
                } else {
                    state.stageDetail.followers = [];
                }
            } catch (error) {
                console.error('Error formatting stage detail for edit:', error);
                // Fallback: set empty arrays if there's an error
                state.stageDetail.approver = [];
                state.stageDetail.followers = [];
            }
        }

        const handleInputNameAdd = async (valueName: any) => {
            if (!valueName) {
                props.dataStage.slug = '';
			} else {
    			props.dataStage.slug = generateStandardSlug(valueName);
			}
		}

        const handleInputNameEdit = async (valueName: any) => {
            if (!valueName) {
                state.stageDetail.slug = '';
			} else {
    			state.stageDetail.slug = generateStandardSlug(valueName);
			}
		}

        const checkDuplicateSlugAdd = (slug: string): boolean => {
            return props.listDataStages.some((item: any) => item.value.slug === slug);
        }

        const checkDuplicateSlugEdit = (slug: string, indexEdit: number): boolean => {
            return props.listDataStages.some((item: any, index: number) => {
                // Bỏ qua kiểm tra nếu là chính slug hiện tại (indexEdit)
                if (index === indexEdit) {
                    return false;
                }
                return item.value.slug === slug;
            });
        }

        return {
            state,
            schemaCreate,
            schemaEdit,
            closeFormStage,
            handleSubmitFormStage,
            editStage,
            removeStage,
            closeEditStage,
            handleSubmitEditStage,
            debouncedGetOptionScopes,
            formatStageDetailForEdit,
            handleInputNameAdd,
            handleInputNameEdit,
            checkDuplicateSlugAdd,
            checkDuplicateSlugEdit
        }
    },
});
</script>
<style type="text/css" scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color:#f8f9fa;
    padding: 10px;
}
svg {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
input[type=checkbox] {
    transform: scale(1.2);
}
.cursor-pointer {
    cursor: pointer;
}
.icon-info {
    font-size: 16px !important;
    color: #0d6efd !important;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>