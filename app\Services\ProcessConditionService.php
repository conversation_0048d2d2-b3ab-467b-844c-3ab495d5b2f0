<?php

namespace App\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ProcessConditionService
{
    /**
     * Kiểm tra điều kiện từ các filter rules
     */
    public function checkCondition($field_values, $processInstance, $or_conditions, $user): bool 
    {
        $filterRules = $or_conditions;
        $formData = $field_values;
        
        // Chuẩn bị Context Data cho Service
        $contextData = [
            'user' => $user,
            'process' => $processInstance
        ];

        // Kiểm tra điều kiện
        $isValid = $this->checkAllFilterConditions(
            $filterRules,
            $formData,
            $contextData
        );

        return $isValid;
    }

    /**
     * Kiểm tra tất cả các filter condition
     */
    public function checkAllFilterConditions(array $filterRules, array $formData, $contextData = null): bool
    {
        if (empty($filterRules)) {
            return true; // Không có quy tắc, mặc định là hợp lệ
        }

        $overallResult = false; // Sẽ bị ghi đè bởi kết quả của khối đầu tiên
        $isFirstBlock = true;
        foreach ($filterRules as $index => $ruleGroup) {
            $objectType = $ruleGroup['object'] ?? null;
            $filterBlock = $ruleGroup['filter'] ?? null;
            $operatorBetweenBlocks = strtolower($ruleGroup['logicalOperator'] ?? 'and'); // Mặc định là AND

            // --- Xác thực cấu trúc cơ bản ---
            if (
                !$objectType
                || !$filterBlock
                || !isset($filterBlock['ors'])
                || !is_array($filterBlock['ors'])
            ) {
                // Cấu trúc sai -> Toàn bộ không hợp lệ
                return false;
            }
            
            // --- Lấy nguồn dữ liệu phù hợp ---
            $dataSource = null;
            if ($objectType === 'form') {
                $dataSource = $formData;
            } elseif ($objectType === 'process') {
                if ($contextData === null) {
                    // Thiếu context cho process -> Không hợp lệ
                    return false;
                }
                $dataSource = $contextData; // Nguồn là mảng context ['user'=>..., 'process'=>...]
            } else {
                // Object không hỗ trợ -> Không hợp lệ
                return false;
            }

            // --- Đánh giá logic AND bên trong khối hiện tại ---
            $conditions = $filterBlock['ors'];
            $currentBlockResult = $this->evaluateInternalBlockAndLogic($conditions, $dataSource, $objectType);
            
            // --- Kết hợp kết quả khối này với kết quả tổng thể ---
            if ($isFirstBlock) {
                $overallResult = $currentBlockResult;
                $isFirstBlock = false;
            } else {
                // Áp dụng toán tử logic kết nối của khối hiện tại
                if ($operatorBetweenBlocks === 'and') {
                    $overallResult = $overallResult && $currentBlockResult;
                } elseif ($operatorBetweenBlocks === 'or') {
                    $overallResult = $overallResult || $currentBlockResult;
                } else {
                    $overallResult = $overallResult && $currentBlockResult; // Mặc định dùng AND nếu toán tử lạ
                }
            }
        }
        
        return $overallResult;
    }

    /**
     * Đánh giá logic AND trong một block
     */
    public function evaluateInternalBlockAndLogic(array $conditions, $dataSource, string $objectType): bool
    {
        // Khối không có điều kiện thì luôn đúng với logic AND
        if (empty($conditions)) {
            return true;
        }
        
        foreach ($conditions as $condition) {
            if (!$this->evaluateSingleCondition($condition, $dataSource, $objectType)) {
                // Một điều kiện sai -> Cả khối sai
                return false;
            }
        }
        // Tất cả điều kiện đều đúng
        return true;
    }

    /**
     * Đánh giá một điều kiện đơn
     */
    public function evaluateSingleCondition(array $condition, $dataSource, string $objectType): bool
    {
        $field = $condition['f'] ?? null; // Tên trường logic
        $operator = $condition['o'] ?? null; // Toán tử so sánh
        $parameter = $condition['p'] ?? null; // Giá trị để so sánh

        // Chuẩn hóa operator thành chữ thường để dễ so sánh
        $operator = $operator ? strtolower($operator) : null;

        if (!$field || !$operator) {
            return false;
        }
        
        // Lấy giá trị thực tế từ nguồn dữ liệu tương ứng
        $actualValue = null;
        if ($objectType === 'form') {
            $actualValue = data_get($dataSource, $field);
        } elseif ($objectType === 'process') {
            $actualValue = $this->getContextValue($dataSource, $field);
        } else {
            // Trường hợp này không nên xảy ra nếu check ở hàm chính
            return false;
        }
        
        // Thực hiện so sánh dựa trên operator
        switch ($operator) {
            case config('constants.operator.EQUAL'):
                // 1. Kiểm tra bằng nghiêm ngặt (===) trước tiên.
                if ($actualValue === $parameter) {
                    return true;
                }
                // 2. Xử lý trường hợp cả hai đều là số (hoặc chuỗi số).
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return $actualValue == $parameter;
                }
                // 3. Mặc định cuối cùng: So sánh lỏng lẻo (==)
                return $actualValue == $parameter;
            case config('constants.operator.NOT_EQUAL'):
                // 1. Kiểm tra không bằng nghiêm ngặt (!==). Nếu kiểu hoặc giá trị khác nhau -> true.
                if ($actualValue === $parameter) {
                    return false; // Nếu bằng nghiêm ngặt thì không phải là 'not_equal'
                }
                // 2. Xử lý trường hợp cả hai đều là số (hoặc chuỗi số).
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    // So sánh không bằng sau khi biết cả hai là số.
                    return $actualValue != $parameter;
                }
                // 3. Mặc định cuối cùng: So sánh lỏng lẻo (==)
                return $actualValue != $parameter;
            case config('constants.operator.GREATER'): // Lớn hơn (>)
                // Ưu tiên 1: So sánh số (nguyên và thực)
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    // So sánh dưới dạng số thực (float) để đảm bảo độ chính xác
                    return (float) $actualValue > (float) $parameter;
                }
                // Ưu tiên 2: So sánh ngày/giờ/thời gian
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter))
                {
                    try {
                        // Thử parse cả hai giá trị thành đối tượng Carbon
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return $dateActual->greaterThan($dateParam);
                    } catch (\Exception $e) {
                        return false;
                    }
                }
                return false;
            case config('constants.operator.LOWER'): // Nhỏ hơn (<)
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float) $actualValue < (float) $parameter;
                }
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return $dateActual->lessThan($dateParam);
                    } catch (\Exception $e) {
                        return false;
                    }
                }
                return false;
            case config('constants.operator.NOT_GREATER'): // Nhỏ hơn hoặc bằng (<=)
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float) $actualValue <= (float) $parameter;
                }
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return $dateActual->lessThanOrEqualTo($dateParam);
                    } catch (\Exception $e) {
                        return false;
                    }
                }
                return false;
            case config('constants.operator.NOT_LOWER'): // Lớn hơn hoặc bằng (>=)
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return (float) $actualValue >= (float) $parameter;
                }
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return $dateActual->greaterThanOrEqualTo($dateParam);
                    } catch (\Exception $e) {
                        return false;
                    }
                }
                return false;
            case config('constants.operator.IN'): // Nằm trong (Thuộc)
                if (!is_array($parameter)) {
                    return false;
                }
                if (isset($parameter[0]) && is_array($parameter[0]) && isset($parameter[0]['value'])) {
                    $allowedValues = array_column($parameter, 'value');
                    return in_array($actualValue, $allowedValues);
                }
                return in_array($actualValue, $parameter);
            case config('constants.operator.NOT_IN'): // Không nằm trong (Không thuộc)
                if (!is_array($parameter)) {
                    return false;
                }
                if (isset($parameter[0]) && is_array($parameter[0]) && isset($parameter[0]['value'])) {
                    $disallowedValues = array_column($parameter, 'value');
                    return !in_array($actualValue, $disallowedValues);
                }
                return !in_array($actualValue, $parameter);
            case config('constants.operator.CONTAIN'): // Chứa cụm từ
                if (!is_string($actualValue) || $actualValue === '') {
                    return false;
                }
                if (!is_string($parameter)) {
                    return false;
                }
                return Str::contains($actualValue, $parameter, true);
            case config('constants.operator.NOT_CONTAIN'): // Không chứa cụm từ
                if (!is_string($actualValue) || $actualValue === '') {
                    return true;
                }
                if (!is_string($parameter)) {
                    return true;
                }
                return !Str::contains($actualValue, $parameter, true);
            case config('constants.operator.EMPTY'): // Trống
                return empty($actualValue);
            case config('constants.operator.NOT_EMPTY'): // Không trống
                return !empty($actualValue);
            case config('constants.operator.BETWEEN'): // Thuộc khoảng
                if (!is_array($parameter) || count($parameter) !== 2) {
                    return false;
                }
                [$startParam, $endParam] = array_values($parameter);
                if (is_numeric($actualValue) && is_numeric($startParam) && is_numeric($endParam)) {
                    $val = (float)$actualValue;
                    $min = min((float)$startParam, (float)$endParam);
                    $max = max((float)$startParam, (float)$endParam);
                    return $val >= $min && $val <= $max;
                }
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($startParam) && !empty($startParam) && !is_numeric($startParam) &&
                    is_string($endParam) && !empty($endParam) && !is_numeric($endParam))
                {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateStart = Carbon::parse($startParam);
                        $dateEnd = Carbon::parse($endParam);
                        return $dateActual->between($dateStart, $dateEnd, true);
                    } catch (\Exception $e) {
                        return false;
                    }
                }
                return false;
            case config('constants.operator.OTHER'): // Khác
                if ($actualValue === $parameter) {
                    return false;
                }
                if (is_numeric($actualValue) && is_numeric($parameter)) {
                    return $actualValue != $parameter;
                }
                if (is_string($actualValue) && !empty($actualValue) && !is_numeric($actualValue) &&
                    is_string($parameter) && !empty($parameter) && !is_numeric($parameter)
                ) {
                    try {
                        $dateActual = Carbon::parse($actualValue);
                        $dateParam = Carbon::parse($parameter);
                        return !$dateActual->equalTo($dateParam);
                    } catch (\Exception $e) {
                        return true;
                    }
                }
                if (is_array($parameter) && isset($parameter['value']) && !isset($parameter[0])) {
                    return $actualValue != $parameter['value'];
                }
                return $actualValue != $parameter;
            default:
                return false;
        }
    }

    /**
     * Lấy giá trị từ context
     */
    public function getContextValue(array $contextData, string $logicalFieldName)
    {
        if (empty($contextData['user']) || empty($contextData['process'])) {
            return null;
        }
        $user = $contextData['user'];
        $process = $contextData['process'];

        $value = null;
        $targetObject = null;
        $actualFieldName = null;

        // Các trường kiểm tra theo User hiện tại
        $userCheckFields = ['department_ids', 'job_position_ids', 'rank_ids'];
        if (in_array($logicalFieldName, $userCheckFields)) {
            $targetObject = $user;
            $userFieldMapping = [
                'department_ids' => 'department_id',
                'job_position_ids' => 'position_id',
                'rank_ids' => 'rank_id',
            ];
            $actualFieldName = $userFieldMapping[$logicalFieldName] ?? null;
        }
        
        // Các trường kiểm tra theo bản ghi Process hiện tại
        $processCheckFields = ['name', 'description', 'created_by'];
        if (in_array($logicalFieldName, $processCheckFields)) {
            $targetObject = $process;
            $actualFieldName = $logicalFieldName;
        }

        // Nếu không thuộc loại nào ở trên
        if ($targetObject === null || $actualFieldName === null) {
            return null;
        }

        try {
            if ($targetObject instanceof Model) {
                $value = $targetObject->{$actualFieldName} ?? null;
            } elseif (is_object($targetObject)) {
                $value = $targetObject->{$actualFieldName} ?? null;
            } elseif (is_array($targetObject)) {
                $value = Arr::get($targetObject, $actualFieldName);
            }
        } catch (\Throwable $e) {
            $value = null;
        }

        return $value;
    }
} 