import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";
import { Meta, Link } from "@/types/index";

export default function usePermissions() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);
    const paginate = reactive({
        meta: {
            from: '',
            to: '',
            total: '',
            lastPage: '',
            currentPage: '',
            perPage: '',
        } as Meta,
        links: {
            prev: '',
            next: '',
        } as Link,
    });

    const dataPermissions = ref<any>([]);
    const dataCounts = ref<any>([]);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const storePermission = async (formPermissionData: any) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/permissions`, formPermissionData);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storePermission(formPermissionData);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    const storePermissionGroup = async (formPermissionGroupData: any) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/permissions/groups`, formPermissionGroupData);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storePermissionGroup(formPermissionGroupData);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    const getAllPermissions = async (
        page: number, 
        perPage: number,
        valueTabActived: string,
    ) => {
        setIsLoading.value = true;
        try {
            const tab = valueTabActived !== '' ? { tab: valueTabActived } : null;
            let response = await axios.get('/api/permissions', {
                params: {
                    tab: tab !== null ? valueTabActived : tab,
                    page: page,
                    perPage: perPage,
                }
            });
            
            if (response.data.status == 'success') {
                dataCounts.value          = response.data.counts;
                dataPermissions.value     = response.data.permissions.data;
                paginate.links.prev       = response.data.permissions.links.prev;
                paginate.links.next       = response.data.permissions.links.next; 
                paginate.meta.currentPage = response.data.permissions.current_page;
                paginate.meta.perPage     = response.data.permissions.per_page;
                paginate.meta.from        = response.data.permissions.from;
                paginate.meta.to          = response.data.permissions.to;
                paginate.meta.total       = response.data.permissions.total;
                paginate.meta.lastPage    = response.data.permissions.last_page;
            }

            const query = {
                ...tab
            };

            await router.push({ name: 'PermissionListData', query: { ...query, page, perPage,  } }).catch(()=>{});
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getAllPermissions(page, perPage, valueTabActived);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        } 
    }

    const getPermissionGroups = async () => {
        try {
            let response = await axios.get('/api/permissions/groups');
            return response?.data;
        } catch (error: any) {
            console.log(error);
            if (!error.response) {
                setTimeout(() => {
                    getPermissionGroups();
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    return {
        setIsLoading,
        paginate,
        dataPermissions,
        getAllPermissions,
        storePermission,
        dataCounts,
        getPermissionGroups,
        storePermissionGroup
    }
}