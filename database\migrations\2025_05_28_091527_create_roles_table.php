<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->unique();
            $table->boolean('is_hidden');
            $table->uuid('tenant_id')->nullable();
            $table->uuid('create_by');
            $table->uuid('update_by')->nullable();
            $table->date('expired_at')->nullable();
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('roles', function (Blueprint $table) {
            $table->index(['id', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
