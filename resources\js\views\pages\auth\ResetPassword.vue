<template>
	<div class="wrapper min-vh-100 d-flex flex-row align-items-center">
		<CContainer>
			<CRow class="justify-content-center">
				<CCol :md="4" class="w-form">
                    <h4 class="text-center mb-4 title-logo text-uppercase">
						{{ $t('auth.title_logo') }}
					</h4>
					<CCardGroup>
						<CCard class="p-4">
							<CCardBody>
								<CForm @submit.prevent="sendResetPassword" v-if="state.linkExpired == false">
									<h5 class="text-center text-title text-uppercase mb-4" v-if="state.sentResetPassword == false">
										{{ $t('auth.retrieve_pass') }}
									</h5>
									<CInputGroup class="mb-4" v-if="state.sentResetPassword == false">
										<CInputGroupText>
											<span class="material-symbols-outlined">mail</span>
										</CInputGroupText>
										<CFormInput
											type="email"
											:placeholder="$t('auth.email_reset')"
										/>
									</CInputGroup>
									<div class="text-center mb-4" v-else>
										<svg class="mb-4" xmlns="http://www.w3.org/2000/svg" height="60px" viewBox="0 -960 960 960" width="60px" fill="rgb(0, 152, 56)">
											<path d="m419-283 294-294-66-66-228 228-111-111-65 66 176 177Zm61.14 228Q392-55 314.51-88.08q-77.48-33.09-135.41-91.02-57.93-57.93-91.02-135.27Q55-391.72 55-479.86 55-569 88.08-646.49q33.09-77.48 90.86-134.97 57.77-57.48 135.19-91.01Q391.56-906 479.78-906q89.22 0 166.83 33.45 77.6 33.46 135.01 90.81t90.89 134.87Q906-569.34 906-480q0 88.28-33.53 165.75t-91.01 135.28q-57.49 57.8-134.83 90.89Q569.28-55 480.14-55Z"/>
										</svg>
										<p>Chúng tôi đã gửi một liên kết đặt lại mật khẩu đến email của bạn. Vui lòng kiểm tra email và làm theo hướng dẫn để đổi mật khẩu.</p>
									</div>
									<div class="d-grid mb-4">
                                        <CButton 
                                            type="submit"
                                            color="primary" 
                                            class="px-4"
                                        >
                                        	<span v-if="state.sentResetPassword == false">{{ $t('auth.confirm_reset') }}</span>
											<router-link 
												v-else
												:to="{ name: 'login'}" 
											>
												<span class="text-white">{{ $t('auth.back_login') }}</span>
											</router-link>
                                        </CButton>
                                    </div>
								</CForm>
								<div class="text-center mb-4" v-else>
									Liên kết đã hết hạn
								</div>
								<div class="d-flex justify-content-center" v-if="state.sentResetPassword == false || state.linkExpired == true">
									<router-link 
										:to="{ name: 'login'}" 
										class="text-link" 
									>
										{{ $t('auth.back_login') }}
									</router-link>
								</div>
							</CCardBody>
						</CCard>
					</CCardGroup>
				</CCol>
			</CRow>
		</CContainer>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive } from 'vue'
import useAuth from '@/composables/auths'

export default defineComponent({
    name: "ResetPassword",

    setup() {
        const state = reactive({
            email: '',
			sentResetPassword : false,
			linkExpired: false, 
        });

        const { resetPassword } = useAuth();

        const sendResetPassword = async (): Promise<void> => {			
            await resetPassword(state.email);
			state.sentResetPassword = true;
			state.linkExpired = true;
        }

        return {
            state,
            sendResetPassword
        }
    }
});
</script>

<style scoped>
	.w-form {
		min-width: 460px !important;
	}
    .title-logo {
		font-size: 24px;
    	color: #6f42c1;
		font-weight: 600;
	}
    .text-title {
		color: #6f42c1;
	}
	.text-link {
		color: #6f42c1;
		font-size: 15px;
		text-decoration: none;
	}
</style>