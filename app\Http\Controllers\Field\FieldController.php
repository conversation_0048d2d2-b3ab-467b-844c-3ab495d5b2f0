<?php

namespace App\Http\Controllers\Field;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Repositories\ModelSystem\ModelSystemRepositoryInterface;

class FieldController extends Controller
{
    private $modelSystemRepository;

    public function __construct(
        ModelSystemRepositoryInterface $modelSystemRepository
    ) 
    {
        $this->modelSystemRepository = $modelSystemRepository;
    }
    
    public function getTables()
    {
        try {
            $models = $this->modelSystemRepository->getModelSystem();
            // L<PERSON>y danh sách các bảng được phép từ key của $models
            $allowed_tables = array_keys($models);
        
            // Lấy danh sách tất cả các bảng trong cơ sở dữ liệu
            $table_alls = DB::select('SHOW TABLES');
            $table_names = array_map('current', $table_alls);
        
            // Lọc danh sách bảng
            $tables = array_intersect($table_names, $allowed_tables);
        
            // Gắn mô tả cho từng bảng
            $result = array_map(function ($table) use ($models) {
                $model = $models[$table];
                return [
                    'table_name'  => $table,
                    'description' => method_exists($model, 'getDescription') ? $model::getDescription() : __('tables.not_object'),
                ];
            }, $tables);
        
            return response()->json(array_values($result));
        } catch (\Exception $e) {
            return response()->json(['status' => 'error'], 500);
        }        
    }

    public function getColumns(Request $request)
    {
        $table = $request->table;

        if (!$table) {
            return response()->json([], 400);
        }

        // Lấy danh sách cột của bảng
        $columns = Schema::getColumnListing($table);

        // Danh sách các cột được phép cho từng bảng
        $allowedColumns = [
            'users' => ['id', 'account_name', 'full_name', 'email'],
            'departments' => ['id', 'name', 'description'],
        ];

        if (array_key_exists($table, $allowedColumns)) {
            $columns = array_intersect($columns, $allowedColumns[$table]);
        } else {
            $columns = [];
        }

        // Lấy mô tả cột từ tệp ngôn ngữ
        $columnDescriptions = __('columns.' . $table);
        
        if (!is_array($columnDescriptions)) {
            $columnDescriptions = [];
        }

        // Kết hợp tên cột và mô tả
        $result = [];
        foreach ($columns as $column) {
            $result[] = [
                'column_name' => $column,
                'description' => $columnDescriptions[$column] ?? $column, // Nếu không có mô tả, sử dụng tên cột
            ];
        }

        return response()->json($result);
    }

    public function getColumnData(Request $request)
    {
        $table  = $request['object_table'];
        $column = $request['column_table'];
        $sub_column_table = $request['sub_column_table'];
        $search = $request['query'];
        if (is_string($column) && is_array($sub_column_table)) {
            $select_columns = array_merge([$column], $sub_column_table);
        } else {
            $select_columns = [$column];
        }
        // Xây dựng truy vấn
        $query = DB::table($table)->select('id', ...$select_columns);

        // Thêm điều kiện tìm kiếm nếu có từ khóa
        if ($search) {
            $query->where($column, 'LIKE', '%' . $search . '%');
        }

        // Lấy dữ liệu từ cột đã chọn
        $data_options = $query->limit(10)->get();
        
        $columnDescriptions = __('columns.'. $table);

        if (!is_array($columnDescriptions)) {
            $columnDescriptions = [];
        }

        // $sub_column_table_description các key là giá trị tương ứng trong $sub_column_table
        if (isset($sub_column_table)) {
            $sub_column_table_description = array_combine(
                $sub_column_table,
                array_map(function ($column) use ($columnDescriptions) {
                    return $columnDescriptions[$column] ?? $column;
                }, $sub_column_table)
            );
        } else {
            $sub_column_table_description = [];
        }
        
        $result = [
            'data_options' => $data_options,
            'sub_column_table_description' => $sub_column_table_description,
        ];

        return response()->json($result);
    }
}
