<?php

// <PERSON>hai báo namespace tương ứng với vị trí file
namespace App\Enums;

use Illuminate\Support\Str;

// Định nghĩa Enum với kiểu giá trị nền là string
enum FieldStatus: int
{
    // Định nghĩa các trường hợp (cases) có thể có và giá trị chuỗi tương ứng
    case TRUE = 1; // Hoạt động
    case FALSE = 0; // Không hoạt động

    public function label(): string
    {
        // 1. Xác định tiền tố (tên file ngôn ngữ)
        $file = 'enums';

        // 2. Tạo phần giữa của key từ tên lớp Enum (WorkflowStatus -> workflow_status)
        $enum_key = Str::snake(class_basename(static::class));

        // 3. Lấy phần cuối của key từ giá trị của case hiện tại
        $case_value = $this->value;

        // 4. <PERSON>h<PERSON><PERSON> lại thành key đầy đủ
        $translation_key = "{$file}.{$enum_key}.{$case_value}";

        // 5. <PERSON><PERSON><PERSON> về bản dịch bằng hàm helper __()
        return __($translation_key);
    }
}