<?php

namespace App\Http\Controllers\Api\File;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;

class FileController extends Controller
{
    public function show(Request $request, string $filename)
    {
        try {
            // Kiểm tra quyền truy cập qua token từ URL hoặc API authentication
            if (!Auth::guard('api')->check()) {
                $token = $request->query('token');
                if (!$token || !$this->validateToken($token)) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
            }

            // Đường dẫn file trong storage
            $path = 'file_uploads/' . $filename;

            // Kiểm tra file có tồn tại không
            if (!Storage::exists($path)) {
                return response()->json(['message' => 'File not found'], 404);
            }

            // Lấy mime type của file
            $mimeType = Storage::mimeType($path);

            // Đọc nội dung file
            $content = Storage::get($path);

            // Tạo response với file
            return Response::make($content, 200, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . $filename . '"',
                'Cache-Control' => 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma' => 'no-cache',
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error processing file'], 500);
        }
    }

    public function download(Request $request, string $filename)
    {
        try {
            // Kiểm tra quyền truy cập qua token từ URL hoặc API authentication
            if (!Auth::guard('api')->check()) {
                $token = $request->query('token');
                if (!$token || !$this->validateToken($token)) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
            }

            // Đường dẫn file trong storage
            $path = 'file_uploads/' . $filename;

            // Kiểm tra file có tồn tại không
            if (!Storage::exists($path)) {
                return response()->json(['message' => 'File not found'], 404);
            }

            // Trả về file để download
            return Storage::download($path, $filename, [
                'Cache-Control' => 'no-store, no-cache, must-revalidate, max-age=0',
                'Pragma' => 'no-cache',
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error downloading file'], 500);
        }
    }

    /**
     * Validate token using Laravel Passport
     *
     * @param string $token
     * @return bool
     */
    private function validateToken(string $token): bool
    {
        try {
            // Simpler approach: set the token and check with Auth guard
            $request = request();
            $request->headers->set('Authorization', 'Bearer ' . $token);
            
            // Let Laravel Passport validate the token
            return Auth::guard('api')->check();
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
