<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conditions', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('name', 200);  // Tên điều kiện
            $table->string('slug', length: 200);  // Slug điều kiện
            $table->json('or_conditions');  // Các nhóm điều kiện
            $table->uuid('process_version_id');  // Thuộc phiên bản quy trình
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('conditions', function (Blueprint $table) {
            $table->index(['id', 'process_version_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conditions');
    }
};
