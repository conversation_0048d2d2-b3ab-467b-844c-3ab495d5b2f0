<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name', 255);
            $table->string('slug', 255)->unique();
            $table->uuid('permission_group_id');
            $table->boolean('is_hidden');
            $table->boolean('is_active')->default(true)->comment('0 - Không hoạt động, 1 - Hoạt động');
            $table->uuid('create_by');
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('permissions', function (Blueprint $table) {
            $table->index(['id', 'permission_group_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
};
