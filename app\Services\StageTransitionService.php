<?php

namespace App\Services;

use App\Models\Stage;
use App\Models\ProcessVersion;

class StageTransitionService
{
    protected $conditionService;

    public function __construct(ProcessConditionService $conditionService)
    {
        $this->conditionService = $conditionService;
    }

    /**
     * <PERSON><PERSON><PERSON> danh sách các stage tiếp theo từ stage transitions
     */
    public function getFromStageInStageTransitions($field_values, $processInstance, $currentStageId, $actionId, $processVersion, $user, $conditionIds)
    {
        $stageTransitions = $processVersion->stageTransitions->where('from_stage_id', $currentStageId)->where('action_id', $actionId);
        $fromStageIds = [];
        
        if ($stageTransitions->isNotEmpty()) {
            foreach ($stageTransitions as $stageTransition) {
                // Lấy các điều kiện liên quan đến StageTransition
                $stageTransitionConditions = $stageTransition->stageTransitionConditions;
                
                if ($stageTransitionConditions->isNotEmpty()) {
                    // <PERSON><PERSON><PERSON> trữ hoặc xử lý các điều kiện
                    $conditionIds = is_string($conditionIds) ? json_decode($conditionIds, true) : $conditionIds;
                    foreach ($stageTransitionConditions as $stageTransitionCondition) {
                        if (is_null($conditionIds) || in_array($stageTransitionCondition->condition_id, $conditionIds)) {
                            $condition = $stageTransitionCondition->condition;
                            $or_conditions = $condition->or_conditions;
                            $checkCondition = $this->conditionService->checkCondition($field_values, $processInstance, $or_conditions, $user);
                            $normalizedCheckCondition = is_bool($checkCondition) ? var_export($checkCondition, true) : strtolower($checkCondition);
                            $normalizedConditionStatus = is_bool($stageTransitionCondition->condition_status) ? var_export($stageTransitionCondition->condition_status, true) : strtolower($stageTransitionCondition->condition_status);

                            if ($normalizedCheckCondition === $normalizedConditionStatus) {
                                $fromStageIds[] = $stageTransition->to_stage_id;
                            }
                        }
                    }
                } else {
                    // Nếu không có điều kiện, mặc định thêm `to_stage_id`
                    $fromStageIds[] = $stageTransition->to_stage_id;
                }
            }
            
            // Loại bỏ các giá trị trùng lặp và trả về kết quả
            $fromStageIds = array_unique($fromStageIds);
            return $fromStageIds;
        }
        
        return [];
    }

    /**
     * Lấy danh sách các stage tiếp theo từ process transitions
     */
    public function getFromStageInProcessTransitions($currentStageId, $actionId, $processVersion)
    {
        $from_stages = $processVersion->processTransitions->where('from_stage_id', $currentStageId)->where('action_id', $actionId);
        
        if ($from_stages->isNotEmpty()) {
            $fromStageIds = $from_stages->pluck('to_stage_id')->unique()->toArray(); // Lấy các stage ID duy nhất
            return $fromStageIds;
        }
        
        return [];
    }
} 