<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\SoftDeletes;

class JobFieldValue extends Model
{
    use SoftDeletes, HasUuids;
    protected $table = 'job_field_values';
    protected $primaryKey = 'id';

    protected $fillable = [
        'job_id',
        'field_id',
        'field_value',
    ];
    
    protected $casts = [
        'field_value' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];
    
    /**
     * L<PERSON>y các đối tượng liên kết với giá trị trường công việc
     */
    public function valueObjects()
    {
        return $this->hasMany(JobFieldValueObject::class, 'job_field_value_id', 'id');
    }

    /**
     * Lấy quan hệ với model Field thông qua field_id
     */
    public function field()
    {
        return $this->belongsTo(Field::class, 'field_id', 'id');
    }
}
