<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('save_jobs', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->string('name', 200);  // Tên công việc
            $table->uuid('user_id');  // Nhân sự cần tạo đơn
            $table->uuid('department_id');  // Phòng ban
            $table->uuid('job_position_id');  // Vị trí
            $table->uuid('process_version_id');  // Áp dụng phiên bản quy trình
            $table->string('status', 20)->comment(' pending - Chờ xử lý, processing - Đang xử lý, completed - <PERSON><PERSON><PERSON> thành, cancel - Không xử lý');  // Trạng thái
            $table->text('description')->nullable();  // <PERSON><PERSON> tả công việc
            $table->json('files')->nullable();  // Tài liệu đính kèm
            $table->json('followers')->nullable();  // Người theo dõi
            $table->json('managers');  // Người quản lý
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();
            $table->softDeletes();
        });

        // Tạo chỉ mục
        Schema::table('save_jobs', function (Blueprint $table) {
            $table->index(['id', 'user_id', 'process_version_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('save_jobs');
    }
};
