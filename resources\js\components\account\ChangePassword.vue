<template>
    <CRow>
        <CCol :xs="12">
            <CCardBody>
                <CForm @submit.prevent="saveChangePassword" class="row gx-3 gy-3 align-items-center">
                    <CCol :md="12">
                        <CFormLabel>
                            {{ $t('change_password.current_password') }} 
                            <span class="text-danger">*</span>
                        </CFormLabel>
                        <CInputGroup>
                            <CFormInput
                                type="password"
                                v-model="state.form.current_password"
                                :placeholder="$t('change_password.placeholder_current_password')"
                                autocomplete="current_password"
                                required
                            />
                        </CInputGroup>
                    </CCol>
                    <CCol :md="12">
                        <CFormLabel>
                            {{ $t('change_password.new_password') }} 
                            <span class="text-danger">*</span>
                        </CFormLabel>
                        <CInputGroup>
                            <CFormInput
                                :type="state.showNewPassword ? 'text' : 'password'"
                                v-model="state.form.new_password"
                                :placeholder="$t('change_password.placeholder_new_password')"
                                autocomplete="new_password"
                                required
                            />
                            <span class="password-toggle-icon material-symbols-outlined" @click="state.showNewPassword = !state.showNewPassword">
                                {{ state.showNewPassword ? 'visibility' : 'visibility_off' }}
                            </span>
                        </CInputGroup>
                    </CCol>
                    <CCol :md="12">
                        <CFormLabel>
                            {{ $t('change_password.new_password_confirm') }} 
                            <span class="text-danger">*</span>
                        </CFormLabel>
                        <CInputGroup>
                            <CFormInput
                                :type="state.showNewPasswordConfirm ? 'text' : 'password'"
                                v-model="state.form.new_password_confirm"
                                :placeholder="$t('change_password.placeholder_new_password_confirm')"
                                autocomplete="new_password_confirm"
                                required
                            />
                            <span class="password-toggle-icon material-symbols-outlined" @click="state.showNewPasswordConfirm = !state.showNewPasswordConfirm">
                                {{ state.showNewPasswordConfirm ? 'visibility' : 'visibility_off' }}
                            </span>
                        </CInputGroup>
                    </CCol>
                    <!-- <CCardFooter>
                        <div class="text-center">
                            <CCol :xs="12">
                                <CButton 
                                    type="submit"
                                    class="btn btn-primary col-2 btn-width"
                                >
                                    {{ $t('change_password.save') }} 
                                </CButton>
                            </CCol>
                        </div>
                    </CCardFooter> -->
                </CForm>
            </CCardBody>
        </CCol>
	</CRow>
    <loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive } from 'vue'
import useAuth from '@/composables/auths'
import Loading from '@/views/loading/Loading.vue'


export default defineComponent({
    name: 'ChangePassword',
    emits: ['close-modal'],

    components: {
        Loading
    },

    setup(props: any, {emit}) {
        const state = reactive({
            showNewPassword: false,
            showNewPasswordConfirm: false,
            form: {
                current_password: '',
                new_password: '',
                new_password_confirm: '',
            }
            
        });

        const { setIsLoading, updateChangePassword } = useAuth();

        const saveChangePassword = async (): Promise<void> => {			
            let result = await updateChangePassword({...state.form});
            if (result) {
                state.form = {
                    current_password: '',
                    new_password: '',
                    new_password_confirm: '',
                }
                emit('close-modal');
            }
        }

        return {
            state,
            setIsLoading,
            saveChangePassword
        }
    },
});
</script>

<style type="text/css" scoped>
    .card-footer {
        left: 0;
        bottom: 0;
        width: 100%;
        background-color:#f8f9fa;
        padding: 10px;
    }
    .password-toggle-icon {
		position: absolute;
		top: 50%;
		right: 10px;
		transform: translateY(-50%);
		cursor: pointer;
		color: #757575;
		background: none;
		border: none;
		outline: none;
		user-select: none;
		z-index: 10;
	}
	.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
		border-radius: 3px !important;
	}
</style>