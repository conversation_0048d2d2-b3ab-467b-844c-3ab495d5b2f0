<template>
    <BModal
        centered
        scrollable
        no-close-on-backdrop
        no-close-on-esc
        size="lg"
        :title="$t('user.update_role')"
        v-model="isVisible"
        @hidden="handleClose"
    >
        <div v-if="setIsLoading" class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">{{ $t('common.loading') }}</span>
            </div>
        </div>

        <div v-else-if="user">
            <!-- User Information Section -->
            <div class="user-info-section mb-4">
                <CForm class="row g-3">
                    <CCol :md="6">
                        <CFormLabel class="text-secondary">
                            {{ $t('user.account_name') }}
                        </CFormLabel>
                        <div class="fw-normal">
                            {{ user.account_name || $t('common.no_data') }}
                        </div>
                    </CCol>
                    <CCol :md="6">
                        <CFormLabel class="text-secondary">
                            {{ $t('user.full_name') }}
                        </CFormLabel>
                        <div class="fw-normal">
                            {{ user.full_name || $t('common.no_data') }}
                        </div>
                    </CCol>
                </CForm>
            </div>

            <!-- Roles Section -->
            <div class="roles-section">
                <!-- Current Roles -->
                <div class="mb-4">
                    <h6 class="mb-2">{{ $t('user.current_roles') }}</h6>
                    <div v-if="selectedRoles.length > 0">
                        <div class="roles-list">
                            <div
                                v-for="role in selectedRoles"
                                :key="role.id"
                                class="role-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded"
                            >
                                <div class="role-info">
                                    <div class="role-name">{{ role.name }}</div>
                                </div>
                                <CButton
                                    color="danger"
                                    variant="outline"
                                    size="sm"
                                    @click="removeRole(role.id)"
                                >
                                    {{ $t('common.remove') }}
                                </CButton>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-center py-3 border rounded bg-light">
                        <div class="text-muted">
                            <span class="material-symbols-outlined fs-4 d-block mb-1">group_off</span>
                            {{ $t('user.no_roles_assigned') }}
                        </div>
                    </div>
                </div>

                <!-- Available Roles -->
                <div class="mb-4">
                    <h6 class="mb-2">{{ $t('user.available_roles') }}</h6>
                    <div v-if="availableRoles.length > 0">
                        <div class="available-roles-list">
                            <div
                                v-for="role in availableRoles"
                                :key="role.id"
                                class="role-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded bg-light"
                            >
                                <div class="role-info">
                                    <div class="role-name">{{ role.name }}</div>
                                </div>
                                <CButton
                                    color="primary"
                                    variant="outline"
                                    size="sm"
                                    @click="addRole(role)"
                                >
                                    {{ $t('common.add') }}
                                </CButton>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-center py-3 border rounded bg-light">
                        <div class="text-muted">
                            {{ $t('user.all_roles_assigned') }}
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        {{ $t('user.roles_updated_at') }}: {{ formatDate(user.roles_updated_at) }}
                    </small>
                </div>
            </div>
        </div>

        <div v-else class="text-center py-4">
            <div class="text-muted">
                <span class="material-symbols-outlined fs-1 d-block mb-2">error</span>
                {{ $t('user.failed_to_load') }}
            </div>
        </div>

        <template #footer>
            <div class="d-flex justify-content-end w-100">
                <CButton color="secondary" @click="handleClose" class="me-2">
                    {{ $t('common.close') }}
                </CButton>
                <CButton
                    color="primary"
                    @click="saveRoles"
                    :disabled="setIsLoading || !hasChanges"
                >
                    <span v-if="setIsLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                    {{ $t('common.save') }}
                </CButton>
            </div>
        </template>
    </BModal>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import useRoles from '@/composables/role'
import useUsers from '@/composables/user'
import { useToast } from 'vue-toast-notification'
import moment from 'moment'

export default defineComponent({
    name: 'UserRole',

    props: {
        visible: {
            type: Boolean,
            default: false
        },
        userData: {
            type: Object as () => any,
            default: null
        }
    },

    emits: ['close', 'updated'],

    setup(props, { emit }) {
        const { t } = useI18n()
        const { getAllRoleOptions, dataRoleOptions } = useRoles()
        const { setIsLoading, updateUserRoles } = useUsers()
        const $toast = useToast()

        const isVisible = ref(props.visible)
        const user = ref(props.userData)
        const selectedRoles = ref<any[]>([])
        const originalRoleIds = ref<string[]>([])

        const formatDate = (date: string) => {
            if (!date) return t('common.no_data');
            return moment(date).format('DD/MM/YYYY HH:mm');
        }

        // Computed property for available roles (not selected)
        const availableRoles = computed(() => {
            const selectedRoleIds = selectedRoles.value.map(role => role.id);
            return dataRoleOptions.value.filter((role: any) => !selectedRoleIds.includes(role.id));
        })

        // Check if there are changes
        const hasChanges = computed(() => {
            const currentRoleIds = selectedRoles.value.map((role: any) => role.id).sort();
            const originalIds = [...originalRoleIds.value].sort();
            return JSON.stringify(currentRoleIds) !== JSON.stringify(originalIds);
        })

        const loadAllRoles = async () => {
            try {
                await getAllRoleOptions();
            } catch (error) {
                console.error('Error loading roles:', error)
            }
        }

        const initializeData = () => {
            if (props.userData) {
                user.value = props.userData
                selectedRoles.value = props.userData.roles ? [...props.userData.roles] : [];
                originalRoleIds.value = props.userData.roles ? props.userData.roles.map((role: any) => role.id) : [];
            }
        }

        const addRole = (role: any) => {
            if (!selectedRoles.value.find(r => r.id === role.id)) {
                selectedRoles.value.push(role);
            }
        }

        const removeRole = (roleId: string) => {
            selectedRoles.value = selectedRoles.value.filter(role => role.id !== roleId);
        }

        const saveRoles = async () => {
            if (!user.value || !hasChanges.value) return;
            const roleIds = selectedRoles.value.map(role => role.id);
            const result = await updateUserRoles(user.value.id, roleIds);
            if (result && result.status === 'success') {
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                })

                // Update original role IDs
                originalRoleIds.value = [...roleIds];

                // Emit updated event with the updated user data from API response
                emit('updated');
                handleClose();
            }
        }

        const handleClose = () => {
            isVisible.value = false;
            // Reset to original state
            if (props.userData) {
                selectedRoles.value = props.userData.roles ? [...props.userData.roles] : [];
            }
            emit('close');
        }

        // Watch for visibility changes
        watch(() => props.visible, (newVal) => {
            isVisible.value = newVal;
            if (newVal) {
                initializeData();
                loadAllRoles();
            }
        })

        // Watch for userData changes
        watch(() => props.userData, (newVal) => {
            if (newVal) {
                initializeData();
            }
        })

        return {
            isVisible,
            setIsLoading,
            user,
            selectedRoles,
            availableRoles,
            hasChanges,
            formatDate,
            addRole,
            removeRole,
            saveRoles,
            handleClose
        }
    }
})
</script>

<style scoped>
.user-info-section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
}

.role-item {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.role-item:hover {
    background-color: #e9ecef;
}

.roles-list {
    max-height: 300px;
    overflow-y: auto;
}
</style>