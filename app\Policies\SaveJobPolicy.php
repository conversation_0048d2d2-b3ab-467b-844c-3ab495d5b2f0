<?php

namespace App\Policies;

use App\Models\SaveJob;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SaveJobPolicy
{
    use HandlesAuthorization;

    /**
     * Kiểm tra quyền xem tất cả công việc
     * Chỉ xem những công việc mà người dùng có quyền xem
     */
    public function viewAny(User $user): bool
    {
        // Tất cả người dùng đã xác thực đều có thể xem danh sách công việc
        // Họ sẽ chỉ thấy những công việc mà họ có quyền xem
        // thông qua repository và service
        return true;
    }

    /**
     * Kiểm tra quyền xem job
     */
    public function view(User $user, SaveJob $job): bool
    {
        return $job->canBeViewedBy($user);
    }

    /**
     * Kiểm tra quyền quản lý job
     */
    public function manager(User $user, SaveJob $job): bool
    {
        return $job->isManager($user) || $job->isCreator($user);
    }

    /**
     * Kiểm tra quyền phê duyệt job
     */
    public function approve(User $user, SaveJob $job): bool
    {
        $pendingApprovals = $job->getAllApprovalStagesForUser($user);
        return $pendingApprovals !== null && $pendingApprovals->isNotEmpty();
    }

    /**
     * Kiểm tra quyền theo dõi/bỏ theo dõi job
     */
    public function toggleFollow(User $user, SaveJob $job): bool
    {
        return $job->canBeViewedBy($user);
    }
} 