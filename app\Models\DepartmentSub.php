<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class DepartmentSub extends Model
{
    use HasUuids;
    
    protected $table = 'department_subs';
    protected $primaryKey = 'id';

    protected $fillable = [
        'date_start',
        'date_end',
        'department_id',
        'rank_id',
        'job_position_id',
        'user_id',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
