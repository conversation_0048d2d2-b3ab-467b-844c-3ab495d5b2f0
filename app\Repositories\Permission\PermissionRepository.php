<?php
namespace App\Repositories\Permission;

use App\Repositories\EloquentRepository;
use App\Enums\PermissionStatus;

class PermissionRepository extends EloquentRepository implements PermissionRepositoryInterface
{
	public function getModel()
	{
		return \App\Models\Permission::class;
	}

	public function getAllPermissions($dataSearch)
	{
		// Chuyển đổi tab thành enum PermissionStatus
		$tabStatus = isset($dataSearch['tab']) && !empty($dataSearch['tab']) 
			? PermissionStatus::fromString($dataSearch['tab']) 
			: PermissionStatus::ALL;

        $page = $dataSearch['page'] ?? null;
        $perPage = $dataSearch['perPage'] ?? null;
		
		$select_columns = [
			'id',
			'name',	
			'slug',
			'permission_group_id',
			'is_hidden',
			'is_active',
			'create_by',
            'created_at',
		];
		
		$query = $this->model
			->with(['permissionGroup:id,name,is_active', 'createBy:id,full_name'])
			->select($select_columns);

        // Tạo baseQuery để sử dụng cho việc đếm các tab
        $baseQuery = clone $query;

        // Áp dụng điều kiện theo tab
        $tabStatus->applyToQuery($query);
        
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';
        $query->orderBy($orderBy, $orderDirection);
        
        // Lấy danh sách trạng thái từ enum
        $statusList = PermissionStatus::cases();
        $counts = [];
        foreach ($statusList as $status) {
            $countQuery = clone $baseQuery;
            $status->applyToQuery($countQuery);
            $counts[strtolower($status->name)] = $countQuery->count();
        }

        if ($page && $perPage) {
            $permissions = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $permissions = $query->get();
        }
        
        return [
            'permissions' => $permissions,
            'counts' => $counts
        ];
	}
}
?>