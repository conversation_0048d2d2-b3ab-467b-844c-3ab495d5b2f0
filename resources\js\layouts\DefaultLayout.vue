<template>
	<div>
		<AppSidebar />
		<div class="wrapper d-flex flex-column min-vh-100">
		<AppHeader />
		<div class="body flex-grow-1">
			<CContainer class="px-4" fluid>
				<router-view />
			</CContainer>
		</div>
		<AppFooter />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { CContainer } from '@coreui/vue';
import AppFooter from '@/components/AppFooter.vue';
import AppHeader from '@/components/AppHeader.vue';
import AppSidebar from '@/components/AppSidebar.vue';

export default defineComponent({
	name: "DefaultLayout",

	components: {
		AppFooter,
		AppHeader,
		AppSidebar,
		CContainer,
	}
});
</script>


