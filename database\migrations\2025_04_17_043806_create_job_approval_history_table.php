<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_approval_history', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('job_id');  // ID công việc
            $table->uuid('stage_id');  // ID giai đoạn duyệt
            $table->uuid('user_id')->nullable();  // ID người đã thực hiện
            $table->json('approved_list')->nullable();  // Danh sách người duyệt
            $table->json('followers')->nullable();  // Danh sách người theo dõi
            $table->uuid('action_id')->nullable();  // ID hành động thực hiện
            $table->dateTime('date')->nullable(); // Thời gian xử lý
            $table->text('comment')->nullable(); // Ý kiến duyệt
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_approval_history');
    }
};
