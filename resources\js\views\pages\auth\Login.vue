<template>
	<div class="wrapper min-vh-100 d-flex flex-row align-items-center">
		<CContainer>
			<CRow class="justify-content-center">
				<CCol :md="4" class="w-form">
					<h4 class="text-center mb-4 title-logo text-uppercase">
						{{ $t('auth.title_logo') }}
					</h4>
					<CCardGroup>
						<CCard class="p-4">
							<CCardBody>
								<CForm @submit.prevent="login">
									<div class="d-flex flex-wrap justify-content-between align-items-center mb-4">
										<h5 class="text-title text-uppercase mb-3 mb-md-0">
											{{ $t('auth.login') }}
										</h5>
										<div>
											<b-dropdown variant="light" class="m-2 dropdown-border" no-caret style="min-width: 150px; width: 150px;">
												<!-- Nút dropdown -->
												<template #button-content>
													<div class="d-flex align-items-center">
														<img :src="selectedLanguage.flag" alt="Current Flag" class="flag-icon mr-2" />
														<span>{{ selectedLanguage.name }}</span>
													</div>
												</template>
												<!-- <PERSON><PERSON><PERSON> m<PERSON> trong dropdown -->
												<b-dropdown-item @click="setLanguage('vi')">
													<div class="d-flex align-items-center">
														<img src="@/assets/images/lang/vn.svg" alt="Cờ Việt Nam" class="flag-icon mr-2" />
														<span>{{ $t('auth.lang.vi') }}</span>
													</div>
												</b-dropdown-item>
												<b-dropdown-item @click="setLanguage('en')">
													<div class="d-flex align-items-center">
														<img src="@/assets/images/lang/en.svg" alt="Cờ Vương quốc Anh" class="flag-icon mr-2" />
														<span>{{ $t('auth.lang.en') }}</span>
													</div>
												</b-dropdown-item>
											</b-dropdown>
										</div>
									</div>
									<CInputGroup class="mb-3">
										<CInputGroupText>
											<span class="material-symbols-outlined">person</span>
										</CInputGroupText>
										<CFormInput
											type="text"
											:placeholder="$t('auth.account_name')"
											autocomplete="account_name"
											v-model="state.user.account_name"
										/>
									</CInputGroup>
									<CInputGroup class="mb-4">
										<CInputGroupText>
											<span class="material-symbols-outlined">lock</span>
										</CInputGroupText>
										<CFormInput
											:type="state.showPassword ? 'text' : 'password'"
											:placeholder="$t('auth.password')"
											autocomplete="password"
											v-model="state.user.password"
										/>
										<span class="password-toggle-icon material-symbols-outlined" @click="state.showPassword = !state.showPassword">
											{{ state.showPassword ? 'visibility' : 'visibility_off' }}
										</span>
									</CInputGroup>
									<div class="d-flex justify-content-center mb-4">
										<router-link 
											:to="{ name: 'ResetPassword'}" 
											class="text-link" 
										>
											{{ $t('auth.reset_pass') }}
										</router-link>
									</div>
									<div class="d-grid">
										<CButton 
											type="submit"
											color="primary" 
											class="px-4"
										>
											{{ $t('auth.login') }}
										</CButton>
									</div>
								</CForm>
							</CCardBody>
						</CCard>
					</CCardGroup>
				</CCol>
			</CRow>
		</CContainer>
	</div>

	<loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { Auth } from "@/types/index";
import Loading from '@/views/loading/Loading.vue'
import { useToast } from 'vue-toast-notification'
import vi from '@/assets/images/lang/vn.svg';
import en from '@/assets/images/lang/en.svg';
import { useI18n } from "vue-i18n";
import { changeAppLanguage } from '@/utils/languageUtils';

export default defineComponent({
  	name: "Login",

	components: {
        Loading,
    },

    setup() {
        const authStore = useAuthStore();
        const $toast = useToast();
        const setIsLoading = ref(false);
		const { t } = useI18n();
        
        const state = reactive({
			showPassword: false,
            user: {
                account_name: '',
                password: ''
            } as Auth,
            rememberMe: false
        });

        const login = async (): Promise<void> => {
            if (!state.user.account_name || !state.user.password) {
                $toast.open({
                    message: 'Please enter your account name and password',
                    type: 'error',
                    duration: 5000,
                    dismissible: true,
                    position: 'bottom-right'
                });
                return;
            }
            
            setIsLoading.value = true;
            
            try {
                const result = await authStore.login(
                    state.user.account_name,
                    state.user.password,
                    state.rememberMe
                );
                
                // Check if result is an object with success property (error case)
                if (result && typeof result === 'object' && 'success' in result && !result.success) {
                    $toast.open({
                        message: result.message || 'Invalid login credentials',
                        type: 'error',
                        duration: 5000,
                        dismissible: true,
                        position: 'bottom-right'
                    });
                }
                // If success is true, no need to do anything as the router will redirect
            } catch (error) {
                console.error('Login error:', error);
                $toast.open({
                    message: 'An unexpected error occurred during login',
                    type: 'error',
                    duration: 5000,
                    dismissible: true,
                    position: 'bottom-right'
                });
            } finally {
                setIsLoading.value = false;
            }
        }
		
		// Initialize selectedLanguage based on localStorage or fall back to system default
		const currentLang = localStorage.getItem('app_language') || 'vi';
		const selectedLanguage = ref({
			name: t(`auth.lang.${currentLang}`),
			flag: currentLang === 'vi' ? vi : en
		});

		const setLanguage = (lang: string) => {
			// Sử dụng hàm changeAppLanguage để thay đổi ngôn ngữ một cách nhất quán
			changeAppLanguage(lang);
			
			// Cập nhật giao diện của dropdown
			if (lang === 'vi') {
				selectedLanguage.value = {
					name: t('auth.lang.vi'),	
					flag: vi
				};
			} else if (lang === 'en') {
				selectedLanguage.value = {
					name: t('auth.lang.en'),
					flag: en
				};
			}
		};

        return {
            state,
            login,
			setIsLoading,
			selectedLanguage,
			setLanguage
        }
    }
});
</script>

<style scoped>
	.w-form {
		min-width: 460px !important;
	}
	.title-logo {
		font-size: 24px;
    	color: #6f42c1;
		font-weight: 600;
	}
	.text-title {
		color: #6f42c1;
	}
	.text-link {
		color: #6f42c1;
		font-size: 15px;
		text-decoration: none;
	}
	.password-toggle-icon {
		position: absolute;
		top: 50%;
		right: 10px;
		transform: translateY(-50%);
		cursor: pointer;
		color: #757575;
		background: none;
		border: none;
		outline: none;
		user-select: none;
		z-index: 10;
	}
	.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
		border-radius: 3px !important;
	}
	/* Loại bỏ màu nền của nút dropdown */
	:deep(.btn-light) {
		background-color: transparent !important;
		border: none !important;
		padding: 6px 12px !important; /* Thêm padding để giống nút */
	}

	/* Thêm viền cho nút dropdown */
	.dropdown-border {
		border: 1px solid #dee2e6; /* Màu xanh nhạt giống trong ảnh */
		border-radius: 8px; /* Bo góc giống ảnh */
	}

	/* Thêm viền cho menu thả xuống */
	:deep(.dropdown-menu) {
		border: 1px solid #dee2e6; /* Màu xanh nhạt giống trong ảnh */
		border-radius: 8px;
		margin-top: 4px; /* Khoảng cách giữa nút và menu */
	}

	/* Điều chỉnh khoảng cách giữa ảnh và chữ */
	.flag-icon {
		width: 24px; 
		height: 18px;
	}

	/* Khoảng cách giữa ảnh và chữ */
	.mr-2 {
		margin-right: 8px;
	}
</style>