import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";
import { Meta, Link } from "@/types/index";
import { log } from 'mathjs';

export default function useJobs() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);
    const paginate = reactive({
        meta: {
            from: '',
            to: '',
            total: '',
            lastPage: '',
            currentPage: '',
            perPage: '',
        } as Meta,
        links: {
            prev: '',
            next: '',
        } as Link,
    });

    const dataJobs = ref<any>([]);
    const dataCounts = ref<any>([]);
    const jobDetail = ref<any>(null);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const storeJob = async (formData: any, config: object) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/jobs`, formData, config);
            
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storeJob(formData, config);
                }, 1000);

                return;
            }
            switch (error.response.status) {
                case 400:
                    if (error.response.data.error_code == 'KEY_DUPLICATE') {
                        $toast.open({
                            message: t('toast.error_code.KEY_DUPLICATE'),
                            type: "warning",
                            duration: 5000,
                            dismissible: true,
                            position: "bottom-right",
                        });
                    }
                    break;
                case 500:
                    $toast.open({
                        message: t('toast.error_code.SERVER_ERROR'),
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });
                    break;
                default:
                    catchError(error);
                    break;
            }
        } finally {
            setIsLoading.value = false;
        }
    };

    const getAllJobs = async (
        page: number, 
        perPage: number,
        valueTabActived: string,
    ) => {
        setIsLoading.value = true;
        try {
            const tab = valueTabActived !== '' ? { tab: valueTabActived } : null;
            let response = await axios.get('/api/jobs', {
                params: {
                    tab: tab !== null ? valueTabActived : tab,
                    page: page,
                    perPage: perPage,
                }
            });
            console.log(response);
            
            if (response.data.status == 'success') {
                dataCounts.value          = response.data.counts;
                dataJobs.value            = response.data.jobs.data;
                paginate.links.prev       = response.data.jobs.links.prev;
                paginate.links.next       = response.data.jobs.links.next; 
                paginate.meta.currentPage = response.data.jobs.current_page;
                paginate.meta.perPage     = response.data.jobs.per_page;
                paginate.meta.from        = response.data.jobs.from;
                paginate.meta.to          = response.data.jobs.to;
                paginate.meta.total       = response.data.jobs.total;
                paginate.meta.lastPage    = response.data.jobs.last_page;
            }

            const query = {
                ...tab
            };

            await router.push({ name: 'JobListData', query: { ...query, page, perPage,  } }).catch(()=>{});
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getAllJobs(page, perPage, valueTabActived);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        } 
    }

    const showDetailJob = async (jobID: string) => {
        setIsLoading.value = true;
        try {
            let response = await axios.get(`/api/jobs/${jobID}`); 
            console.log(response);
            if (response.data.status === true) {
                jobDetail.value = response.data.data;
                return response.data.data;
            } else {
                $toast.open({
                    message: response.data.message || 'Không tìm thấy thông tin công việc',
                    type: "warning",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                return null;
            }
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    showDetailJob(jobID);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        } 
    }

    const getFileName = (filePath: string): string => {
        return filePath.split('/').pop() || filePath;
    };

    const getFileUrl = (filePath: string): string => {
        const filename = getFileName(filePath);
        return `/api/files/${encodeURIComponent(filename)}`;
    };

    const getDownloadUrl = (filePath: string): string => {
        const filename = getFileName(filePath);
        return `/api/files/download/${encodeURIComponent(filename)}`;
    };

    const isImageFile = (filePath: string): boolean => {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
        return imageExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
    };

    const isPdfFile = (filePath: string): boolean => {
        return filePath.toLowerCase().endsWith('.pdf');
    };

    const isPreviewableFile = (filePath: string): boolean => {
        return isImageFile(filePath) || isPdfFile(filePath);
    };

    const downloadFile = async (filePath: string): Promise<void> => {
        try {
            const response = await fetch(getDownloadUrl(filePath));

            if (!response.ok) {
                throw new Error('Download failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = getFileName(filePath);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading file:', error);
            throw error;
        }
    };

    const updateExecuteAction = async (formData: any, config: object) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/jobs/execute`, formData, config);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    updateExecuteAction(formData, config);
                }, 1000);
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    }

    const updateExecuteActionBackTo = async (jobID: string, actionBackToId: string, stageId: string, backToStageId: string, comment: string, pendingApprovalId: string, emailConditionIdsBackTo: string[]) => {
        setIsLoading.value = true;
        try {
            let response = await axios.put(`/api/jobs/${jobID}/execute-back-to`, {
                action_back_to_id: actionBackToId,
                stage_id: stageId,
                back_to_stage_id: backToStageId,
                comment: comment,
                pending_approval_id: pendingApprovalId,
                email_condition_ids_back_to: emailConditionIdsBackTo,
            });
            
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    updateExecuteActionBackTo(jobID, actionBackToId, stageId, backToStageId, comment, pendingApprovalId, emailConditionIdsBackTo);
                }, 1000);
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    }

    const getFieldValuesByStage = async (jobID: string, stageID: string) => {
        setIsLoading.value = true;
        try {
            let response = await axios.get(`/api/jobs/${jobID}/field-values-stage`, {
				params: {
                    stage_id: stageID,
				}
			});

            return response?.data;
        } catch (error: any) {
            console.log(error);
            if (!error.response) {
                setTimeout(() => {
                    getFieldValuesByStage(jobID, stageID);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    }

    return {
        setIsLoading,
        storeJob,
        paginate,
        dataCounts,
        dataJobs,
        jobDetail,
        getAllJobs,
        showDetailJob,
        getFileName,
        getFileUrl,
        getDownloadUrl,
        isImageFile,
        isPdfFile,
        isPreviewableFile,
        downloadFile,
        updateExecuteAction,
        updateExecuteActionBackTo,
        getFieldValuesByStage,
    }
}